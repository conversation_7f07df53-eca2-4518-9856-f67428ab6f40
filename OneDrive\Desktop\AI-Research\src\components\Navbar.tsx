import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>elp, <PERSON>u, Search, User } from 'lucide-react';
import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Animated<PERSON>ogo from './AnimatedLogo';
import SearchBar from './SearchBar';
import ThemeToggle from './ThemeToggle';

export default function Navbar() {
  const [showNotifications, setShowNotifications] = useState(false);
  
  const notifications = [
    {
      id: 1,
      title: 'Research Complete',
      message: 'Your research on "Quantum Computing" is now complete.',
      time: '2 hours ago',
      read: false,
    },
    {
      id: 2,
      title: 'New Sources Found',
      message: '5 new academic sources for "AI Ethics" have been identified.',
      time: 'Yesterday',
      read: true,
    },
    {
      id: 3,
      title: 'Weekly Summary',
      message: 'Your weekly research activity summary is ready.',
      time: '3 days ago',
      read: true,
    }
  ];

  return (
    <motion.nav 
      className="glass-navbar sticky top-0 flex items-center justify-between p-4"
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.4 }}
    >
      <div className="flex items-center gap-3">
        <AnimatedLogo />
      </div>
      
      <div className="hidden md:block">
        <SearchBar />
      </div>
      
      <div className="flex items-center gap-3">
        <ThemeToggle />
        
        <motion.button 
          className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <CircleHelp size={20} className="text-gray-600 dark:text-gray-300" />
        </motion.button>
        
        <div className="relative">
          <motion.button 
            className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors relative"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => setShowNotifications(!showNotifications)}
          >
            <BellRing size={20} className="text-gray-600 dark:text-gray-300" />
            <motion.div 
              className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: "spring", stiffness: 500, damping: 30 }}
            />
          </motion.button>
          
          <AnimatePresence>
            {showNotifications && (
              <motion.div 
                className="absolute right-0 mt-2 w-80 max-h-96 overflow-y-auto rounded-lg shadow-lg glass-effect z-50"
                initial={{ opacity: 0, y: 10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
              >
                <div className="p-3 border-b border-gray-200 dark:border-gray-700">
                  <div className="flex justify-between items-center">
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">Notifications</h3>
                    <span className="text-xs font-medium text-indigo-600 dark:text-indigo-400">Mark all as read</span>
                  </div>
                </div>
                
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {notifications.map((notification) => (
                    <div 
                      key={notification.id} 
                      className={`p-3 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors ${
                        !notification.read ? 'bg-indigo-50/50 dark:bg-indigo-900/10' : ''
                      }`}
                    >
                      <div className="flex gap-3">
                        <div className={`mt-0.5 w-2 h-2 rounded-full flex-shrink-0 ${
                          !notification.read ? 'bg-indigo-500' : 'bg-transparent'
                        }`} />
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 dark:text-gray-100">{notification.title}</h4>
                          <p className="text-xs text-gray-600 dark:text-gray-400 mt-0.5">{notification.message}</p>
                          <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">{notification.time}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="p-3 border-t border-gray-200 dark:border-gray-700 text-center">
                  <button className="text-sm text-indigo-600 dark:text-indigo-400 font-medium hover:text-indigo-700 dark:hover:text-indigo-300">
                    View all notifications
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
        
        <motion.div 
          className="w-9 h-9 rounded-full bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center text-white font-medium shadow-md shadow-indigo-500/20 dark:shadow-indigo-500/10 cursor-pointer"
          whileHover={{ scale: 1.05, boxShadow: '0 0 15px rgba(99, 102, 241, 0.5)' }}
          whileTap={{ scale: 0.95 }}
        >
          JD
        </motion.div>
      </div>
    </motion.nav>
  );
}
