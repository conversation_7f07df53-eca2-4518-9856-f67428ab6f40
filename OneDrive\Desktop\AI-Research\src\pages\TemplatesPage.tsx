import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Layers, 
  Search, 
  Filter, 
  Plus, 
  Star, 
  Download, 
  Eye, 
  Edit,
  Copy,
  Trash2,
  MoreVertical,
  FileText,
  Brain,
  Globe,
  Zap,
  Target,
  BookOpen,
  Users,
  Clock,
  CheckCircle
} from 'lucide-react';
import GlassmorphicCard from '../components/GlassmorphicCard';
import PageTransition from '../components/PageTransition';

type Template = {
  id: number;
  name: string;
  description: string;
  category: string;
  type: 'research' | 'report' | 'analysis' | 'survey';
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: string;
  usageCount: number;
  rating: number;
  isPublic: boolean;
  isPremium: boolean;
  author: string;
  dateCreated: string;
  lastUpdated: string;
  tags: string[];
  sections: string[];
  icon: React.ReactNode;
  preview?: string;
};

type TemplateFilter = 'all' | 'research' | 'report' | 'analysis' | 'survey';
type CategoryFilter = 'all' | 'AI & ML' | 'Climate Science' | 'Biotechnology' | 'Quantum Computing' | 'Space Technology' | 'Energy';

export default function TemplatesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<TemplateFilter>('all');
  const [categoryFilter, setCategoryFilter] = useState<CategoryFilter>('all');
  const [selectedTemplates, setSelectedTemplates] = useState<number[]>([]);

  // Mock templates data
  const [templates] = useState<Template[]>([
    {
      id: 1,
      name: "AI Ethics Research Framework",
      description: "Comprehensive template for conducting ethical analysis of AI systems and their societal impact.",
      category: "AI & ML",
      type: "research",
      difficulty: "Advanced",
      estimatedTime: "4-6 weeks",
      usageCount: 156,
      rating: 4.8,
      isPublic: true,
      isPremium: false,
      author: "Dr. Sarah Chen",
      dateCreated: "2024-01-15",
      lastUpdated: "2024-01-15",
      tags: ["AI Ethics", "Framework", "Societal Impact", "Bias Analysis"],
      sections: ["Executive Summary", "Methodology", "Ethical Framework", "Case Studies", "Recommendations"],
      icon: <Brain className="w-5 h-5" />,
      preview: "This template provides a structured approach to analyzing AI systems from an ethical perspective..."
    },
    {
      id: 2,
      name: "Climate Impact Assessment",
      description: "Template for evaluating environmental impact and climate change implications of technologies.",
      category: "Climate Science",
      type: "analysis",
      difficulty: "Intermediate",
      estimatedTime: "3-4 weeks",
      usageCount: 89,
      rating: 4.6,
      isPublic: true,
      isPremium: false,
      author: "Environmental Research Lab",
      dateCreated: "2024-01-14",
      lastUpdated: "2024-01-14",
      tags: ["Climate Change", "Environmental Impact", "Sustainability", "Carbon Footprint"],
      sections: ["Background", "Impact Metrics", "Data Collection", "Analysis", "Mitigation Strategies"],
      icon: <Globe className="w-5 h-5" />
    },
    {
      id: 3,
      name: "Biotech Innovation Report",
      description: "Structured template for documenting biotechnology research findings and commercial potential.",
      category: "Biotechnology",
      type: "report",
      difficulty: "Advanced",
      estimatedTime: "5-7 weeks",
      usageCount: 67,
      rating: 4.9,
      isPublic: false,
      isPremium: true,
      author: "BioTech Research Institute",
      dateCreated: "2024-01-13",
      lastUpdated: "2024-01-13",
      tags: ["Biotechnology", "Innovation", "Commercial Analysis", "R&D"],
      sections: ["Technology Overview", "Market Analysis", "Competitive Landscape", "Risk Assessment", "Commercialization Strategy"],
      icon: <Zap className="w-5 h-5" />
    },
    {
      id: 4,
      name: "Quantum Computing Survey",
      description: "Template for conducting comprehensive surveys on quantum computing developments and applications.",
      category: "Quantum Computing",
      type: "survey",
      difficulty: "Advanced",
      estimatedTime: "6-8 weeks",
      usageCount: 34,
      rating: 4.7,
      isPublic: true,
      isPremium: true,
      author: "Quantum Research Consortium",
      dateCreated: "2024-01-12",
      lastUpdated: "2024-01-12",
      tags: ["Quantum Computing", "Survey", "Technology Assessment", "Applications"],
      sections: ["Introduction", "Current State", "Key Players", "Applications", "Future Outlook"],
      icon: <Target className="w-5 h-5" />
    },
    {
      id: 5,
      name: "Academic Literature Review",
      description: "Standard template for conducting systematic literature reviews in academic research.",
      category: "AI & ML",
      type: "research",
      difficulty: "Beginner",
      estimatedTime: "2-3 weeks",
      usageCount: 234,
      rating: 4.5,
      isPublic: true,
      isPremium: false,
      author: "Academic Research Hub",
      dateCreated: "2024-01-11",
      lastUpdated: "2024-01-11",
      tags: ["Literature Review", "Academic", "Systematic Review", "Research Methodology"],
      sections: ["Abstract", "Introduction", "Methodology", "Results", "Discussion", "Conclusion"],
      icon: <BookOpen className="w-5 h-5" />
    },
    {
      id: 6,
      name: "Technology Trend Analysis",
      description: "Template for analyzing emerging technology trends and their market implications.",
      category: "AI & ML",
      type: "analysis",
      difficulty: "Intermediate",
      estimatedTime: "3-4 weeks",
      usageCount: 112,
      rating: 4.4,
      isPublic: true,
      isPremium: false,
      author: "Tech Analysis Group",
      dateCreated: "2024-01-10",
      lastUpdated: "2024-01-10",
      tags: ["Technology Trends", "Market Analysis", "Forecasting", "Innovation"],
      sections: ["Trend Identification", "Market Impact", "Adoption Timeline", "Key Players", "Recommendations"],
      icon: <FileText className="w-5 h-5" />
    }
  ]);

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = typeFilter === 'all' || template.type === typeFilter;
    const matchesCategory = categoryFilter === 'all' || template.category === categoryFilter;
    
    return matchesSearch && matchesType && matchesCategory;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'research':
        return <Brain size={16} className="text-blue-500" />;
      case 'report':
        return <FileText size={16} className="text-green-500" />;
      case 'analysis':
        return <Target size={16} className="text-purple-500" />;
      case 'survey':
        return <Users size={16} className="text-orange-500" />;
      default:
        return <FileText size={16} className="text-gray-500" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'Advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'research':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'report':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'analysis':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'survey':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < Math.floor(rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  const handleSelectTemplate = (templateId: number) => {
    setSelectedTemplates(prev => 
      prev.includes(templateId) 
        ? prev.filter(id => id !== templateId)
        : [...prev, templateId]
    );
  };

  return (
    <PageTransition>
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Research Templates</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Pre-built templates to accelerate your research process
                </p>
              </div>
              <div className="flex items-center gap-3">
                <motion.button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Plus size={18} className="mr-2 inline" />
                  Create Template
                </motion.button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search templates..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value as TemplateFilter)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Types</option>
                  <option value="research">Research</option>
                  <option value="report">Report</option>
                  <option value="analysis">Analysis</option>
                  <option value="survey">Survey</option>
                </select>
                
                <select
                  value={categoryFilter}
                  onChange={(e) => setCategoryFilter(e.target.value as CategoryFilter)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Categories</option>
                  <option value="AI & ML">AI & ML</option>
                  <option value="Climate Science">Climate Science</option>
                  <option value="Biotechnology">Biotechnology</option>
                  <option value="Quantum Computing">Quantum Computing</option>
                  <option value="Space Technology">Space Technology</option>
                  <option value="Energy">Energy</option>
                </select>
              </div>
            </div>

            {/* Bulk Actions */}
            {selectedTemplates.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg flex items-center justify-between"
              >
                <span className="text-sm text-indigo-700 dark:text-indigo-300">
                  {selectedTemplates.length} template(s) selected
                </span>
                <div className="flex gap-2">
                  <button className="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700">
                    <Download size={14} className="mr-1 inline" />
                    Download
                  </button>
                  <button className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700">
                    <Copy size={14} className="mr-1 inline" />
                    Duplicate
                  </button>
                </div>
              </motion.div>
            )}
          </div>

          {/* Templates Grid */}
          <div className="flex-1 overflow-y-auto p-6">
            {filteredTemplates.length === 0 ? (
              <div className="text-center py-12">
                <Layers size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No templates found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {searchTerm ? 'Try adjusting your search terms' : 'Create your first research template to get started'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredTemplates.map((template, index) => (
                  <GlassmorphicCard key={template.id} delay={index}>
                    <div className="p-5">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedTemplates.includes(template.id)}
                            onChange={() => handleSelectTemplate(template.id)}
                            className="rounded border-gray-300"
                          />
                          {template.icon}
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(template.type)}`}>
                            {template.type}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          {template.isPremium && (
                            <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300 rounded-full">
                              Pro
                            </span>
                          )}
                          <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                            <MoreVertical size={16} className="text-gray-400" />
                          </button>
                        </div>
                      </div>

                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                        {template.name}
                      </h3>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {template.description}
                      </p>

                      <div className="flex items-center justify-between mb-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(template.difficulty)}`}>
                          {template.difficulty}
                        </span>
                        <div className="flex items-center gap-1">
                          {renderStars(template.rating)}
                          <span className="text-xs text-gray-500 ml-1">({template.rating})</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400 mb-3">
                        <span className="flex items-center gap-1">
                          <Clock size={12} />
                          {template.estimatedTime}
                        </span>
                        <span className="flex items-center gap-1">
                          <Users size={12} />
                          {template.usageCount} uses
                        </span>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-3">
                        {template.tags.slice(0, 3).map(tag => (
                          <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded">
                            {tag}
                          </span>
                        ))}
                        {template.tags.length > 3 && (
                          <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded">
                            +{template.tags.length - 3}
                          </span>
                        )}
                      </div>

                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                        <div className="flex justify-between">
                          <span>By: {template.author}</span>
                          <span>{template.sections.length} sections</span>
                        </div>
                        <div className="mt-1">
                          <span>Updated: {new Date(template.lastUpdated).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <button className="flex-1 px-3 py-2 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
                          <CheckCircle size={14} className="mr-1 inline" />
                          Use Template
                        </button>
                        <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <Eye size={14} />
                        </button>
                        <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <Copy size={14} />
                        </button>
                      </div>
                    </div>
                  </GlassmorphicCard>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
