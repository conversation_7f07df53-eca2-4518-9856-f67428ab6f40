import { ReactNode } from 'react';
import { motion } from 'framer-motion';

type GlassmorphicCardProps = {
  children: ReactNode;
  className?: string;
  onClick?: () => void;
  delay?: number;
  hover?: boolean;
};

export default function GlassmorphicCard({ 
  children, 
  className = '', 
  onClick,
  delay = 0,
  hover = true
}: GlassmorphicCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.5, 
        ease: [0.22, 1, 0.36, 1],
        delay: delay * 0.1
      }}
      whileHover={hover ? { y: -5, transition: { duration: 0.2 } } : undefined}
      className={`backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 border border-gray-200/50 dark:border-gray-700/50 shadow-lg rounded-xl overflow-hidden ${className}`}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
}
