import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Alert<PERSON>riangle, BookOpen, Check, ExternalLink, FileText, Link, Lock, ShieldCheck, ThumbsUp, User, X } from 'lucide-react';
import { Research } from '../context/ResearchContext';

type SourceCredibilityModalProps = {
  isOpen: boolean;
  onClose: () => void;
  research: Research;
};

export default function SourceCredibilityModal({ isOpen, onClose, research }: SourceCredibilityModalProps) {
  const [sources, setSources] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  
  useEffect(() => {
    if (isOpen) {
      setLoading(true);
      
      // Generate simulated sources based on research details
      setTimeout(() => {
        const credibleSourceCount = research.credibleSourcesFound;
        const nonCredibleSourceCount = research.sourcesScanned - credibleSourceCount;
        const generatedSources = [];
        
        // Add credible sources
        for (let i = 0; i < credibleSourceCount; i++) {
          generatedSources.push(generateSource(true, research));
        }

        // Add non-credible sources
        for (let i = 0; i < nonCredibleSourceCount; i++) {
          generatedSources.push(generateSource(false, research));
        }
        
        // Shuffle sources for realistic ordering
        setSources(shuffleArray(generatedSources));
        setLoading(false);
      }, 1000);
    }
  }, [isOpen, research]);
  
  const generateSource = (isCredible: boolean, research: Research) => {
    const sourceTypes = {
      Academic: {
        domains: ['sciencedirect.com', 'springer.com', 'nature.com', 'jstor.org', 'oxfordjournals.org'],
        prefixes: ['Journal of', 'Advances in', 'Review of', 'Studies in'],
      },
      Scientific: {
        domains: ['science.org', 'pnas.org', 'cell.com', 'acs.org', 'ieee.org'],
        prefixes: ['Proceedings of', 'Research on', 'Laboratory', 'Experimental'],
      },
      News: {
        domains: ['nytimes.com', 'reuters.com', 'bbc.com', 'apnews.com', 'bloomberg.com'],
        prefixes: ['Report:', 'Analysis:', 'Special Report:', 'Investigation:'],
      },
      Government: {
        domains: ['gov', 'europa.eu', 'un.org', 'who.int', 'nih.gov'],
        prefixes: ['Official Report on', 'Government Analysis of', 'Public Data on'],
      },
      Industry: {
        domains: ['mckinsey.com', 'deloitte.com', 'gartner.com', 'forrester.com', 'pwc.com'],
        prefixes: ['Industry Perspective on', 'Market Analysis of', 'Sector Report:'],
      },
      Web: {
        domains: ['medium.com', 'wordpress.com', 'blogspot.com', 'substack.com', 'webmd.com'],
        prefixes: ['Guide to', 'Overview of', 'Perspective on'],
      }
    };
    
    // Random source type from selected sources
    const sourceType = research.sources[Math.floor(Math.random() * research.sources.length)];
    const sourceInfo = sourceTypes[sourceType as keyof typeof sourceTypes];
    
    // Generate author names
    const firstNames = ['John', 'Sarah', 'Michael', 'Emily', 'David', 'Jennifer', 'Robert', 'Lisa'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
    const author = `${firstNames[Math.floor(Math.random() * firstNames.length)]} ${lastNames[Math.floor(Math.random() * lastNames.length)]}`;
    
    // Generate publication year based on time range
    const currentYear = new Date().getFullYear();
    const yearRange = research.timeRange === 'all' ? 15 : parseInt(research.timeRange || '5');
    const year = currentYear - Math.floor(Math.random() * yearRange);
    
    // Choose domain and title prefix
    const domain = sourceInfo.domains[Math.floor(Math.random() * sourceInfo.domains.length)];
    const prefix = sourceInfo.prefixes[Math.floor(Math.random() * sourceInfo.prefixes.length)];
    
    // Generate credibility factors
    let credibilityFactors = [];
    if (isCredible) {
      credibilityFactors = [
        { name: 'Author Verification', status: 'verified', icon: <User size={16} className="text-green-600" /> },
        { name: 'Publication Reputation', status: 'high', icon: <ThumbsUp size={16} className="text-green-600" /> },
        { name: 'Peer Reviewed', status: 'yes', icon: <Check size={16} className="text-green-600" /> },
        { name: 'Recent Publication', status: `${year}`, icon: <Check size={16} className="text-green-600" /> }
      ];
      
      // Add some variety to credible sources
      if (Math.random() > 0.3) {
        credibilityFactors[2] = { name: 'Citation Count', status: `${Math.floor(Math.random() * 50) + 10}`, icon: <Link size={16} className="text-green-600" /> };
      }
    } else {
      // For non-credible sources, randomize failure points
      const failureTypes = [
        { name: 'Author Verification', status: 'unknown', icon: <User size={16} className="text-red-600" /> },
        { name: 'Publication Reputation', status: 'low', icon: <AlertTriangle size={16} className="text-red-600" /> },
        { name: 'Peer Reviewed', status: 'no', icon: <X size={16} className="text-red-600" /> },
        { name: 'Citation Count', status: '0', icon: <Link size={16} className="text-red-600" /> },
        { name: 'Contradicted by Multiple Sources', status: 'yes', icon: <AlertTriangle size={16} className="text-red-600" /> },
        { name: 'Outdated Information', status: `${currentYear - yearRange - Math.floor(Math.random() * 5)}`, icon: <AlertTriangle size={16} className="text-red-600" /> }
      ];
      
      // Randomly select 2-3 failure reasons
      const failureCount = Math.floor(Math.random() * 2) + 2;
      const shuffledFailures = shuffleArray([...failureTypes]);
      const selectedFailures = shuffledFailures.slice(0, failureCount);
      
      // Add 1-2 positive factors to make it realistic
      const positiveFactors = [
        { name: 'Recent Publication', status: `${year}`, icon: <Check size={16} className="text-green-600" /> },
        { name: 'Accessible Source', status: 'yes', icon: <Lock size={16} className="text-green-600" /> }
      ];
      
      const positiveCount = Math.floor(Math.random() * 2) + 1;
      const selectedPositives = shuffleArray([...positiveFactors]).slice(0, positiveCount);
      
      credibilityFactors = [...selectedFailures, ...selectedPositives];
    }
    
    return {
      id: Math.random().toString(36).substring(2, 11),
      title: `${prefix} ${research.title}`,
      url: `https://${domain}/article-${Math.random().toString(36).substring(2, 7)}`,
      author,
      year,
      source: domain,
      type: sourceType,
      isCredible,
      credibilityScore: isCredible ? Math.floor(Math.random() * 15) + 85 : Math.floor(Math.random() * 30) + 20,
      credibilityFactors
    };
  };
  
  // Utility function to shuffle an array
  const shuffleArray = (array: any[]) => {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
  };
  
  const filteredSources = filter === 'all' 
    ? sources 
    : filter === 'verified' 
      ? sources.filter(source => source.isCredible) 
      : sources.filter(source => !source.isCredible);
  
  if (!isOpen) return null;
  
  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <motion.div
        className="bg-white rounded-xl w-full max-w-4xl max-h-[90vh] flex flex-col"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg text-green-600">
              <ShieldCheck size={22} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">Source Credibility Analysis</h2>
              <p className="text-sm text-gray-500">{research.title}</p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>
        
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-3">
            <button
              className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                filter === 'all' 
                  ? 'bg-indigo-100 text-indigo-700' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              onClick={() => setFilter('all')}
            >
              All Sources ({research.sourcesScanned})
            </button>
            <button
              className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                filter === 'verified' 
                  ? 'bg-green-100 text-green-700' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              onClick={() => setFilter('verified')}
            >
              Verified ({research.credibleSourcesFound})
            </button>
            <button
              className={`px-3 py-1.5 rounded-full text-sm font-medium ${
                filter === 'rejected' 
                  ? 'bg-red-100 text-red-700' 
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
              onClick={() => setFilter('rejected')}
            >
              Rejected ({research.sourcesScanned - research.credibleSourcesFound})
            </button>
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex flex-col items-center justify-center h-full py-8">
              <div className="w-12 h-12 rounded-full border-4 border-gray-200 border-t-indigo-600 animate-spin mb-4"></div>
              <p className="text-gray-600">Loading source analysis...</p>
            </div>
          ) : filteredSources.length > 0 ? (
            <div className="space-y-4">
              {filteredSources.map((source) => (
                <div 
                  key={source.id} 
                  className={`border rounded-lg overflow-hidden ${
                    source.isCredible ? 'border-green-200' : 'border-red-200'
                  }`}
                >
                  <div className={`p-4 ${source.isCredible ? 'bg-green-50' : 'bg-red-50'}`}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium text-gray-800">{source.title}</h3>
                        <p className="text-sm text-gray-600 mt-1">
                          {source.author} ({source.year}) • {source.source}
                        </p>
                      </div>
                      <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                        source.isCredible 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-red-100 text-red-700'
                      }`}>
                        {source.isCredible ? 'Verified' : 'Not Verified'}
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4 bg-white">
                    <div className="flex items-center gap-2 mb-3">
                      <BookOpen size={16} className="text-gray-500" />
                      <span className="text-sm font-medium text-gray-700">{source.type} Source</span>
                      
                      <div className="flex-1"></div>
                      
                      <div className="flex items-center gap-1">
                        <span className="text-sm text-gray-600">Credibility Score:</span>
                        <div className="w-10 h-10 rounded-full flex items-center justify-center font-bold text-sm text-white bg-gradient-to-br from-green-500 to-blue-500">
                          {source.credibilityScore}
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3">
                      {source.credibilityFactors.map((factor: any, index: number) => (
                        <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                          {factor.icon}
                          <span className="text-sm text-gray-700">{factor.name}</span>
                          <span className="text-xs bg-gray-200 px-2 py-0.5 rounded ml-auto">
                            {factor.status}
                          </span>
                        </div>
                      ))}
                    </div>
                    
                    <div className="mt-4 pt-3 border-t border-gray-100 flex justify-between items-center">
                      <span className="text-xs text-gray-500">
                        Analysis completed on {new Date().toLocaleDateString()}
                      </span>
                      <a 
                        href={source.url}
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="flex items-center gap-1.5 text-sm text-blue-600 hover:text-blue-700"
                      >
                        <span>View Source</span>
                        <ExternalLink size={14} />
                      </a>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">No sources found matching your filter.</p>
              <button 
                className="mt-2 text-indigo-600 hover:text-indigo-700 text-sm font-medium"
                onClick={() => setFilter('all')}
              >
                View all sources
              </button>
            </div>
          )}
        </div>
        
        <div className="border-t border-gray-200 p-4 bg-gray-50 rounded-b-xl flex justify-between items-center">
          <div className="text-sm text-gray-500">
            Showing {filteredSources.length} of {research.sourcesScanned} sources
          </div>
          <button
            onClick={onClose}
            className="px-4 py-2 text-white font-medium rounded-lg bg-indigo-600 hover:bg-indigo-700 transition-colors"
          >
            Close
          </button>
        </div>
      </motion.div>
    </motion.div>
  );
}
