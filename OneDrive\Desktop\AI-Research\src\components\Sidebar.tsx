import { Book<PERSON>pen, CirclePlus, Clock, Compass, Database, FileText, House, Layers, Settings, Share2, Sparkles } from 'lucide-react';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate, useLocation } from 'react-router-dom';

type NavItem = {
  name: string;
  icon: React.ReactNode;
  path: string;
  badge?: string | number;
  badgeColor?: string;
};

export default function Sidebar({ onNewResearch }: { onNewResearch: () => void }) {
  const navigate = useNavigate();
  const location = useLocation();

  const topNavItems: NavItem[] = [
    { name: 'Dashboard', icon: <House size={20} />, path: '/' },
    { name: 'Reports', icon: <FileText size={20} />, path: '/reports', badge: 3 },
    { name: 'Browse', icon: <Compass size={20} />, path: '/browse', badge: 'New', badgeColor: 'green' },
    { name: 'Library', icon: <BookOpen size={20} />, path: '/library' },
    { name: 'History', icon: <Clock size={20} />, path: '/history' },
    { name: 'Sources', icon: <Database size={20} />, path: '/sources' },
    { name: 'Templates', icon: <Layers size={20} />, path: '/templates' },
  ];
  
  const bottomNavItems: NavItem[] = [
    { name: 'Settings', icon: <Settings size={20} />, path: '/settings' },
    { name: 'Upgrade Plan', icon: <Sparkles size={20} className="text-amber-500" />, path: '/upgrade', badge: 'Pro', badgeColor: 'amber' },
  ];

  const handleItemClick = (item: NavItem) => {
    navigate(item.path);
  };

  return (
    <motion.aside 
      className="w-64 border-r border-gray-200 dark:border-gray-800 h-[calc(100vh-64px)] flex flex-col bg-white dark:bg-gray-900"
      initial={{ x: -20, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.4 }}
    >
      <div className="p-4">
        <motion.button 
          className="w-full flex items-center justify-center gap-2 p-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg font-medium transition-all hover:shadow-lg hover:shadow-indigo-500/30 dark:hover:shadow-indigo-500/20"
          onClick={onNewResearch}
          whileHover={{ y: -2 }}
          whileTap={{ scale: 0.98 }}
        >
          <CirclePlus size={18} />
          <span>New Research</span>
        </motion.button>
      </div>
      
      <div className="flex-1 overflow-y-auto">
        <nav className="px-3 py-2">
          <ul className="space-y-1">
            {topNavItems.map((item, index) => (
              <motion.li 
                key={item.name}
                initial={{ x: -20, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <button
                  onClick={() => handleItemClick(item)}
                  className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-md transition-all text-left ${
                    location.pathname === item.path
                      ? 'bg-indigo-50 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300'
                      : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                  }`}
                >
                  {item.icon}
                  <span className="font-medium">{item.name}</span>

                  {item.badge && (
                    <div className={`ml-auto px-1.5 py-0.5 text-xs rounded-full ${
                      item.badgeColor === 'green'
                        ? 'bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300'
                        : item.badgeColor === 'amber'
                        ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300'
                        : 'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300'
                    }`}>
                      {item.badge}
                    </div>
                  )}
                </button>
              </motion.li>
            ))}
          </ul>
          
          <div className="mt-6 mb-4">
            <div className="px-3">
              <h3 className="text-xs uppercase font-semibold text-gray-500 dark:text-gray-400">Recent Projects</h3>
            </div>
            <ul className="mt-2 space-y-1">
              {['AI Ethics Research', 'Climate Data Analysis', 'Blockchain Technology'].map((project) => (
                <li key={project}>
                  <button className="w-full flex items-center gap-3 px-3 py-2 rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 text-left">
                    <div className="w-2 h-2 rounded-full bg-indigo-500 dark:bg-indigo-400"></div>
                    <span className="text-sm truncate">{project}</span>
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </nav>
      </div>
      
      <div className="p-3 border-t border-gray-200 dark:border-gray-800">
        <ul className="space-y-1">
          {bottomNavItems.map((item) => (
            <li key={item.name}>
              <button
                onClick={() => handleItemClick(item)}
                className={`w-full flex items-center gap-3 px-3 py-2.5 rounded-md transition-colors text-left ${
                  location.pathname === item.path
                    ? 'bg-indigo-50 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300'
                    : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                {item.icon}
                <span className="font-medium">{item.name}</span>

                {item.badge && (
                  <div className="ml-auto">
                    <div className={`animate-pulse-slow px-1.5 py-0.5 text-xs rounded-full ${
                      item.badgeColor === 'amber'
                        ? 'bg-amber-100 dark:bg-amber-900/30 text-amber-700 dark:text-amber-300'
                        : 'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-700 dark:text-indigo-300'
                    }`}>
                      {item.badge}
                    </div>
                  </div>
                )}
              </button>
            </li>
          ))}
        </ul>
      </div>
    </motion.aside>
  );
}
