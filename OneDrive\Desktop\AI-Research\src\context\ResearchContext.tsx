import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';

export type ResearchStatus = 'in-progress' | 'completed' | 'paused';

export type Research = {
  id: number;
  title: string;
  description: string;
  progress?: number;
  sourcesScanned: number;
  credibleSourcesFound: number;
  timeElapsed?: string;
  completedDate?: string;
  status: ResearchStatus;
  createdAt: string;
  sources: string[];
  researchDepth?: string;
  timeRange?: string;
  aiModel?: string;
};

type ResearchContextType = {
  researches: Research[];
  addResearch: (research: Omit<Research, 'id' | 'createdAt'>) => void;
  updateResearch: (id: number, updates: Partial<Research>) => void;
  deleteResearch: (id: number) => void;
  getResearch: (id: number) => Research | undefined;
  simulateProgress: (id: number) => void;
  completeResearch: (id: number) => void;
  pauseResearch: (id: number) => void;
  resumeResearch: (id: number) => void;
  getInProgressResearches: () => Research[];
  getCompletedResearches: () => Research[];
};

const ResearchContext = createContext<ResearchContextType | undefined>(undefined);

export const useResearch = () => {
  const context = useContext(ResearchContext);
  if (!context) {
    throw new Error('useResearch must be used within a ResearchProvider');
  }
  return context;
};

// Initial mock data
const initialResearches: Research[] = [
  {
    id: 1,
    title: "Quantum Computing Advancements",
    description: "Latest breakthroughs in quantum computing technologies and applications",
    progress: 67,
    sourcesScanned: 42,
    credibleSourcesFound: 28,
    timeElapsed: "1h 26m",
    status: "in-progress",
    createdAt: "2025-07-01T09:30:00Z",
    sources: ['Academic', 'News', 'Industry']
  },
  {
    id: 2,
    title: "Climate Change Mitigation Strategies",
    description: "Comprehensive analysis of current and emerging climate change solutions",
    progress: 32,
    sourcesScanned: 31,
    credibleSourcesFound: 19,
    timeElapsed: "0h 48m",
    status: "in-progress",
    createdAt: "2025-07-01T11:15:00Z",
    sources: ['Academic', 'Government', 'News']
  },
  {
    id: 3,
    title: "AI Ethics in Healthcare",
    description: "Ethical considerations for artificial intelligence applications in medical settings",
    sourcesScanned: 58,
    credibleSourcesFound: 37,
    completedDate: "June 28, 2025",
    status: "completed",
    createdAt: "2025-06-25T14:20:00Z",
    sources: ['Academic', 'Industry', 'News']
  },
  {
    id: 4,
    title: "Renewable Energy Economics",
    description: "Cost analysis and economic impacts of transitioning to renewable energy sources",
    sourcesScanned: 63,
    credibleSourcesFound: 41,
    completedDate: "June 25, 2025",
    status: "completed",
    createdAt: "2025-06-20T10:45:00Z",
    sources: ['Government', 'Industry', 'Academic']
  }
];

type ResearchProviderProps = {
  children: ReactNode;
};

export const ResearchProvider = ({ children }: ResearchProviderProps) => {
  const [researches, setResearches] = useState<Research[]>(() => {
    const savedResearches = localStorage.getItem('researches');
    return savedResearches ? JSON.parse(savedResearches) : initialResearches;
  });
  
  // Save to localStorage whenever researches change
  useEffect(() => {
    localStorage.setItem('researches', JSON.stringify(researches));
  }, [researches]);
  
  const addResearch = (research: Omit<Research, 'id' | 'createdAt'>) => {
    const newResearch: Research = {
      ...research,
      id: Date.now(),
      createdAt: new Date().toISOString()
    };
    setResearches(prev => [...prev, newResearch]);
    return newResearch.id;
  };
  
  const updateResearch = (id: number, updates: Partial<Research>) => {
    setResearches(prev => 
      prev.map(research => 
        research.id === id ? { ...research, ...updates } : research
      )
    );
  };
  
  const getResearch = (id: number) => {
    return researches.find(research => research.id === id);
  };
  
  const simulateProgress = (id: number) => {
    const research = getResearch(id);
    if (research && research.status === 'in-progress') {
      const currentProgress = research.progress || 0;
      if (currentProgress < 100) {
        // Determine progress speed based on research depth
        const depthMultiplier = research.researchDepth ? parseInt(research.researchDepth) : 2;
        const progressRate = 5 - depthMultiplier; // Higher depth = slower progress
        
        // Simulate scanning more sources
        // More comprehensive research = more sources scanned
        const sourceIncrease = Math.floor(Math.random() * 3) + depthMultiplier;
        const newSourcesScanned = research.sourcesScanned + sourceIncrease;
        
        // Credible sources found depends on the sources selected
        const hasAcademic = research.sources.includes('Academic');
        const hasScientific = research.sources.includes('Scientific');
        const credibilityBonus = (hasAcademic ? 1 : 0) + (hasScientific ? 1 : 0);
        const newCredibleSourcesFound = research.credibleSourcesFound + Math.floor(Math.random() * 2) + credibilityBonus;
        
        // Calculate new progress based on time and depth
        const progressIncrease = Math.floor(Math.random() * progressRate) + 2; // 2-6% increase
        const newProgress = Math.min(currentProgress + progressIncrease, 100);
        
        // Update time elapsed
        const timeElapsedMinutes = parseInt(research.timeElapsed?.split('h ')[1].split('m')[0] || '0');
        const timeElapsedHours = parseInt(research.timeElapsed?.split('h')[0] || '0');
        
        let newMinutes = timeElapsedMinutes + 10; // Add 10 minutes
        let newHours = timeElapsedHours;
        
        if (newMinutes >= 60) {
          newHours += 1;
          newMinutes -= 60;
        }
        
        const newTimeElapsed = `${newHours}h ${newMinutes}m`;
        
        updateResearch(id, {
          progress: newProgress,
          sourcesScanned: newSourcesScanned,
          credibleSourcesFound: newCredibleSourcesFound,
          timeElapsed: newTimeElapsed
        });
        
        // If progress reaches 100%, automatically complete the research
        if (newProgress === 100) {
          completeResearch(id);
        }
      }
    }
  };
  
  const completeResearch = (id: number) => {
    const research = getResearch(id);
    if (research) {
      const currentDate = new Date();
      const formattedDate = currentDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
      
      updateResearch(id, {
        status: 'completed',
        progress: 100,
        completedDate: formattedDate
      });
    }
  };
  
  const pauseResearch = (id: number) => {
    updateResearch(id, { status: 'paused' });
  };
  
  const resumeResearch = (id: number) => {
    updateResearch(id, { status: 'in-progress' });
  };
  
  const deleteResearch = useCallback((id: number) => {
    setResearches(prev => prev.filter(research => research.id !== id));
  }, []);
  
  const getInProgressResearches = () => {
    return researches.filter(research => research.status === 'in-progress');
  };
  
  const getCompletedResearches = () => {
    return researches.filter(research => research.status === 'completed');
  };
  
  const value = {
    researches,
    addResearch,
    updateResearch,
    deleteResearch,
    getResearch,
    simulateProgress,
    completeResearch,
    pauseResearch,
    resumeResearch,
    getInProgressResearches,
    getCompletedResearches
  };
  
  return (
    <ResearchContext.Provider value={value}>
      {children}
    </ResearchContext.Provider>
  );
};
