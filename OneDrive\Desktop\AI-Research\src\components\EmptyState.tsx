import { motion } from 'framer-motion';
import { Brain, Lightbulb } from 'lucide-react';

type EmptyStateProps = {
  title: string;
  description: string;
  action?: React.ReactNode;
  type?: 'research' | 'activity' | 'default';
};

export default function EmptyState({ 
  title, 
  description, 
  action,
  type = 'default'
}: EmptyStateProps) {
  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 }
  };

  return (
    <motion.div 
      className="flex flex-col items-center justify-center p-8 text-center"
      variants={container}
      initial="hidden"
      animate="show"
    >
      <motion.div 
        className="w-16 h-16 mb-4 flex items-center justify-center rounded-full bg-gradient-to-r from-indigo-100 to-purple-100 dark:from-indigo-900/30 dark:to-purple-900/30"
        variants={item}
      >
        {type === 'research' ? (
          <Brain className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
        ) : type === 'activity' ? (
          <motion.div
            animate={{ 
              rotate: [0, 10, 0, -10, 0],
            }}
            transition={{ 
              repeat: Infinity, 
              duration: 5,
              ease: "easeInOut"
            }}
          >
            <Lightbulb className="w-8 h-8 text-amber-500" />
          </motion.div>
        ) : (
          <Brain className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
        )}
      </motion.div>
      
      <motion.h3 
        className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-2"
        variants={item}
      >
        {title}
      </motion.h3>
      
      <motion.p 
        className="text-gray-600 dark:text-gray-400 max-w-md mb-6"
        variants={item}
      >
        {description}
      </motion.p>
      
      {action && (
        <motion.div variants={item}>
          {action}
        </motion.div>
      )}
    </motion.div>
  );
}
