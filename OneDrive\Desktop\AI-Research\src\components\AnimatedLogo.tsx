import { motion } from 'framer-motion';

const AnimatedLogo = () => {
  return (
    <motion.div 
      className="relative flex items-center gap-3"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
    >
      <div className="relative">
        <motion.div 
          className="flex items-center justify-center w-10 h-10 bg-gradient-to-br from-indigo-600 to-purple-600 rounded-lg text-white overflow-hidden shadow-lg shadow-indigo-500/30"
          whileHover={{ scale: 1.05 }}
          transition={{ type: "spring", stiffness: 400, damping: 10 }}
        >
          <motion.div
            initial={{ y: 35 }}
            animate={{ y: 0 }}
            transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
          >
            <span className="font-bold text-xl">R</span>
          </motion.div>
        </motion.div>
        
        <motion.div 
          className="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-br from-blue-500 to-cyan-400 rounded-full shadow-md"
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.3, duration: 0.4, type: "spring" }}
        />
      </div>
      
      <motion.h1 
        className="text-xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent"
        initial={{ x: -20, opacity: 0 }}
        animate={{ x: 0, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        ResearchNexus
      </motion.h1>
    </motion.div>
  );
};

export default AnimatedLogo;
