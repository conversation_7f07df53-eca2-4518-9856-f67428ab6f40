import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  TrendingUp, 
  Search, 
  Filter, 
  Star, 
  Clock, 
  Users, 
  BookOpen,
  Brain,
  Zap,
  Globe,
  Lightbulb,
  Target,
  ArrowRight,
  Plus,
  Eye,
  Heart,
  Share2
} from 'lucide-react';
import GlassmorphicCard from '../components/GlassmorphicCard';
import PageTransition from '../components/PageTransition';

type TrendingTopic = {
  id: number;
  title: string;
  description: string;
  category: string;
  trending: boolean;
  popularity: number;
  researchCount: number;
  lastUpdated: string;
  tags: string[];
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  estimatedTime: string;
  icon: React.ReactNode;
};

type Category = {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  topicCount: number;
};

export default function BrowsePage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');

  const categories: Category[] = [
    {
      id: 'ai-ml',
      name: 'AI & Machine Learning',
      description: 'Artificial intelligence, machine learning, and deep learning topics',
      icon: <Brain className="w-6 h-6" />,
      color: 'from-purple-500 to-indigo-600',
      topicCount: 156
    },
    {
      id: 'climate',
      name: 'Climate Science',
      description: 'Climate change, environmental science, and sustainability',
      icon: <Globe className="w-6 h-6" />,
      color: 'from-green-500 to-emerald-600',
      topicCount: 89
    },
    {
      id: 'biotech',
      name: 'Biotechnology',
      description: 'Genetics, bioengineering, and medical research',
      icon: <Zap className="w-6 h-6" />,
      color: 'from-blue-500 to-cyan-600',
      topicCount: 124
    },
    {
      id: 'quantum',
      name: 'Quantum Computing',
      description: 'Quantum mechanics, quantum algorithms, and quantum systems',
      icon: <Target className="w-6 h-6" />,
      color: 'from-orange-500 to-red-600',
      topicCount: 67
    },
    {
      id: 'space',
      name: 'Space Technology',
      description: 'Aerospace engineering, space exploration, and astronomy',
      icon: <Star className="w-6 h-6" />,
      color: 'from-indigo-500 to-purple-600',
      topicCount: 78
    },
    {
      id: 'energy',
      name: 'Renewable Energy',
      description: 'Solar, wind, and alternative energy technologies',
      icon: <Lightbulb className="w-6 h-6" />,
      color: 'from-yellow-500 to-orange-600',
      topicCount: 92
    }
  ];

  const trendingTopics: TrendingTopic[] = [
    {
      id: 1,
      title: "Large Language Models and Emergent Abilities",
      description: "Exploring the unexpected capabilities that emerge in large-scale language models as they increase in size and complexity.",
      category: "ai-ml",
      trending: true,
      popularity: 95,
      researchCount: 1247,
      lastUpdated: "2024-01-15",
      tags: ["LLM", "Emergent Behavior", "Scaling Laws", "GPT"],
      difficulty: "Advanced",
      estimatedTime: "3-4 weeks",
      icon: <Brain className="w-5 h-5" />
    },
    {
      id: 2,
      title: "Carbon Capture and Storage Technologies",
      description: "Latest developments in capturing atmospheric CO2 and storing it safely to combat climate change.",
      category: "climate",
      trending: true,
      popularity: 87,
      researchCount: 892,
      lastUpdated: "2024-01-14",
      tags: ["Carbon Capture", "Climate Tech", "CCS", "Sustainability"],
      difficulty: "Intermediate",
      estimatedTime: "2-3 weeks",
      icon: <Globe className="w-5 h-5" />
    },
    {
      id: 3,
      title: "CRISPR Gene Editing in Cancer Treatment",
      description: "Revolutionary applications of CRISPR technology for targeted cancer therapy and personalized medicine.",
      category: "biotech",
      trending: true,
      popularity: 91,
      researchCount: 756,
      lastUpdated: "2024-01-13",
      tags: ["CRISPR", "Gene Editing", "Cancer", "Immunotherapy"],
      difficulty: "Advanced",
      estimatedTime: "4-5 weeks",
      icon: <Zap className="w-5 h-5" />
    },
    {
      id: 4,
      title: "Quantum Error Correction Breakthroughs",
      description: "Recent advances in quantum error correction that bring us closer to fault-tolerant quantum computing.",
      category: "quantum",
      trending: true,
      popularity: 83,
      researchCount: 423,
      lastUpdated: "2024-01-12",
      tags: ["Quantum Computing", "Error Correction", "Fault Tolerance"],
      difficulty: "Advanced",
      estimatedTime: "5-6 weeks",
      icon: <Target className="w-5 h-5" />
    },
    {
      id: 5,
      title: "Perovskite Solar Cell Efficiency",
      description: "Breakthrough improvements in perovskite solar cell technology achieving record efficiency rates.",
      category: "energy",
      trending: false,
      popularity: 79,
      researchCount: 634,
      lastUpdated: "2024-01-11",
      tags: ["Solar Energy", "Perovskite", "Photovoltaics", "Efficiency"],
      difficulty: "Intermediate",
      estimatedTime: "2-3 weeks",
      icon: <Lightbulb className="w-5 h-5" />
    },
    {
      id: 6,
      title: "Mars Habitat Construction Technologies",
      description: "Innovative approaches to building sustainable habitats on Mars using in-situ resource utilization.",
      category: "space",
      trending: false,
      popularity: 76,
      researchCount: 298,
      lastUpdated: "2024-01-10",
      tags: ["Mars", "Space Habitats", "ISRU", "Space Colonization"],
      difficulty: "Intermediate",
      estimatedTime: "3-4 weeks",
      icon: <Star className="w-5 h-5" />
    }
  ];

  const filteredTopics = trendingTopics.filter(topic => {
    const matchesSearch = topic.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         topic.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         topic.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === 'all' || topic.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'all' || topic.difficulty === selectedDifficulty;
    
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'Intermediate':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'Advanced':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <PageTransition>
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Browse Research Topics</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Discover trending research topics and start your next investigation
                </p>
              </div>
              <div className="flex items-center gap-3">
                <motion.button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Plus size={18} className="mr-2 inline" />
                  Suggest Topic
                </motion.button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search research topics..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
                
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Levels</option>
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                </select>
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-y-auto">
            {/* Categories Section */}
            <div className="p-6 border-b border-gray-200 dark:border-gray-800">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Research Categories</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.map((category, index) => (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`p-4 rounded-lg bg-gradient-to-r ${category.color} text-white cursor-pointer hover:shadow-lg transition-shadow`}
                    onClick={() => setSelectedCategory(category.id)}
                  >
                    <div className="flex items-center justify-between mb-2">
                      {category.icon}
                      <span className="text-sm font-medium">{category.topicCount} topics</span>
                    </div>
                    <h3 className="font-semibold mb-1">{category.name}</h3>
                    <p className="text-sm opacity-90">{category.description}</p>
                  </motion.div>
                ))}
              </div>
            </div>

            {/* Trending Topics */}
            <div className="p-6">
              <div className="flex items-center gap-2 mb-6">
                <TrendingUp className="text-orange-500" size={24} />
                <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Trending Research Topics</h2>
              </div>

              {filteredTopics.length === 0 ? (
                <div className="text-center py-12">
                  <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No topics found</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    Try adjusting your search terms or filters
                  </p>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {filteredTopics.map((topic, index) => (
                    <GlassmorphicCard key={topic.id} delay={index}>
                      <div className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-center gap-3">
                            {topic.icon}
                            <div>
                              <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                                {topic.title}
                              </h3>
                              {topic.trending && (
                                <div className="flex items-center gap-1 text-orange-500">
                                  <TrendingUp size={14} />
                                  <span className="text-xs font-medium">Trending</span>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center gap-1">
                            <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                              <Heart size={16} className="text-gray-400" />
                            </button>
                            <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                              <Share2 size={16} className="text-gray-400" />
                            </button>
                          </div>
                        </div>

                        <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm leading-relaxed">
                          {topic.description}
                        </p>

                        <div className="flex items-center gap-2 mb-4">
                          <span className={`px-2 py-1 text-xs rounded-full ${getDifficultyColor(topic.difficulty)}`}>
                            {topic.difficulty}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            <Clock size={12} className="inline mr-1" />
                            {topic.estimatedTime}
                          </span>
                          <span className="text-xs text-gray-500 dark:text-gray-400">
                            <Users size={12} className="inline mr-1" />
                            {topic.researchCount} researchers
                          </span>
                        </div>

                        <div className="flex flex-wrap gap-1 mb-4">
                          {topic.tags.slice(0, 4).map(tag => (
                            <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>

                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-xs text-gray-500 dark:text-gray-400">
                            <span>Popularity: {topic.popularity}%</span>
                            <span>Updated: {new Date(topic.lastUpdated).toLocaleDateString()}</span>
                          </div>
                          <div className="flex gap-2">
                            <button className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                              <Eye size={14} className="mr-1 inline" />
                              Preview
                            </button>
                            <button className="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
                              Start Research
                              <ArrowRight size={14} className="ml-1 inline" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </GlassmorphicCard>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
