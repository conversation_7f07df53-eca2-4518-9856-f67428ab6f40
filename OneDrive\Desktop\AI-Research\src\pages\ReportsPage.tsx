import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Download, 
  Search, 
  Filter, 
  Calendar, 
  Eye, 
  Share2, 
  Trash2,
  MoreVertical,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { useResearch } from '../context/ResearchContext';
import GlassmorphicCard from '../components/GlassmorphicCard';
import PageTransition from '../components/PageTransition';

type ReportFilter = 'all' | 'completed' | 'draft' | 'shared';
type SortOption = 'newest' | 'oldest' | 'name' | 'size';

export default function ReportsPage() {
  const { researches } = useResearch();
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<ReportFilter>('all');
  const [sortBy, setSortBy] = useState<SortOption>('newest');
  const [selectedReports, setSelectedReports] = useState<number[]>([]);

  // Generate mock reports from research data
  const generateReports = () => {
    return researches.map(research => ({
      id: research.id,
      title: `${research.title} - Research Report`,
      description: research.description,
      status: research.status === 'completed' ? 'completed' : 'draft',
      createdAt: research.createdAt,
      updatedAt: research.completedDate || research.createdAt,
      size: `${Math.floor(Math.random() * 500 + 100)}KB`,
      pages: Math.floor(Math.random() * 20 + 5),
      downloads: Math.floor(Math.random() * 100),
      shared: Math.random() > 0.7,
      type: 'Research Report',
      tags: ['AI', 'Research', 'Analysis']
    }));
  };

  const [reports] = useState(generateReports());

  const filteredReports = reports
    .filter(report => {
      const matchesSearch = report.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           report.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesFilter = filter === 'all' || 
                           (filter === 'completed' && report.status === 'completed') ||
                           (filter === 'draft' && report.status === 'draft') ||
                           (filter === 'shared' && report.shared);
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case 'oldest':
          return new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime();
        case 'name':
          return a.title.localeCompare(b.title);
        default:
          return 0;
      }
    });

  const handleSelectReport = (reportId: number) => {
    setSelectedReports(prev => 
      prev.includes(reportId) 
        ? prev.filter(id => id !== reportId)
        : [...prev, reportId]
    );
  };

  const handleSelectAll = () => {
    if (selectedReports.length === filteredReports.length) {
      setSelectedReports([]);
    } else {
      setSelectedReports(filteredReports.map(report => report.id));
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'draft':
        return <Clock size={16} className="text-yellow-500" />;
      default:
        return <AlertCircle size={16} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <PageTransition>
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Reports</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Manage and download your research reports
                </p>
              </div>
              <div className="flex items-center gap-3">
                <motion.button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <FileText size={18} className="mr-2 inline" />
                  Generate Report
                </motion.button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1 relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search reports..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value as ReportFilter)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Reports</option>
                  <option value="completed">Completed</option>
                  <option value="draft">Draft</option>
                  <option value="shared">Shared</option>
                </select>
                
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as SortOption)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="name">Name A-Z</option>
                </select>
              </div>
            </div>

            {/* Bulk Actions */}
            {selectedReports.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg flex items-center justify-between"
              >
                <span className="text-sm text-indigo-700 dark:text-indigo-300">
                  {selectedReports.length} report(s) selected
                </span>
                <div className="flex gap-2">
                  <button className="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700">
                    <Download size={14} className="mr-1 inline" />
                    Download
                  </button>
                  <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                    <Trash2 size={14} className="mr-1 inline" />
                    Delete
                  </button>
                </div>
              </motion.div>
            )}
          </div>

          {/* Reports Grid */}
          <div className="flex-1 overflow-y-auto p-6">
            {filteredReports.length === 0 ? (
              <div className="text-center py-12">
                <FileText size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No reports found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {searchTerm ? 'Try adjusting your search terms' : 'Create your first research report to get started'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredReports.map((report, index) => (
                  <GlassmorphicCard key={report.id} delay={index}>
                    <div className="p-5">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedReports.includes(report.id)}
                            onChange={() => handleSelectReport(report.id)}
                            className="rounded border-gray-300"
                          />
                          {getStatusIcon(report.status)}
                        </div>
                        <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                          <MoreVertical size={16} className="text-gray-400" />
                        </button>
                      </div>

                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                        {report.title}
                      </h3>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {report.description}
                      </p>

                      <div className="flex items-center gap-2 mb-3">
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(report.status)}`}>
                          {report.status}
                        </span>
                        {report.shared && (
                          <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 rounded-full">
                            Shared
                          </span>
                        )}
                      </div>

                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-4 space-y-1">
                        <div className="flex justify-between">
                          <span>Size: {report.size}</span>
                          <span>{report.pages} pages</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Downloads: {report.downloads}</span>
                          <span>{new Date(report.updatedAt).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <button className="flex-1 px-3 py-2 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
                          <Eye size={14} className="mr-1 inline" />
                          View
                        </button>
                        <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <Download size={14} />
                        </button>
                        <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <Share2 size={14} />
                        </button>
                      </div>
                    </div>
                  </GlassmorphicCard>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
