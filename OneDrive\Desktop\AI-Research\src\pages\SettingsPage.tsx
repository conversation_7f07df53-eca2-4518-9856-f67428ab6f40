import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Setting<PERSON>, 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Database, 
  Download, 
  Upload,
  Key,
  Globe,
  Monitor,
  Moon,
  Sun,
  Save,
  RefreshCw,
  Trash2,
  Eye,
  EyeOff,
  Check,
  X
} from 'lucide-react';
import GlassmorphicCard from '../components/GlassmorphicCard';
import PageTransition from '../components/PageTransition';
import { useDarkMode } from '../hooks/useDarkMode';

type SettingsSection = 'profile' | 'notifications' | 'privacy' | 'appearance' | 'data' | 'api';

export default function SettingsPage() {
  const [activeSection, setActiveSection] = useState<SettingsSection>('profile');
  const [showPassword, setShowPassword] = useState(false);
  const [unsavedChanges, setUnsavedChanges] = useState(false);
  const { theme, setTheme } = useDarkMode();

  // Profile settings
  const [profileData, setProfileData] = useState({
    name: '<PERSON>. <PERSON>',
    email: '<EMAIL>',
    organization: 'Research University',
    title: 'Senior Research Scientist',
    bio: 'Passionate researcher focused on AI ethics and sustainable technology.',
    location: 'San Francisco, CA',
    website: 'https://alexjohnson.research.com'
  });

  // Notification settings
  const [notifications, setNotifications] = useState({
    emailNotifications: true,
    researchUpdates: true,
    weeklyDigest: false,
    collaborationInvites: true,
    systemAlerts: true,
    marketingEmails: false
  });

  // Privacy settings
  const [privacy, setPrivacy] = useState({
    profileVisibility: 'public',
    researchVisibility: 'collaborators',
    allowDataCollection: true,
    shareAnalytics: false,
    twoFactorAuth: false
  });

  // Data settings
  const [dataSettings, setDataSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    dataRetention: '2years',
    exportFormat: 'json'
  });

  const settingsSections = [
    { id: 'profile', name: 'Profile', icon: <User size={20} /> },
    { id: 'notifications', name: 'Notifications', icon: <Bell size={20} /> },
    { id: 'privacy', name: 'Privacy & Security', icon: <Shield size={20} /> },
    { id: 'appearance', name: 'Appearance', icon: <Palette size={20} /> },
    { id: 'data', name: 'Data & Storage', icon: <Database size={20} /> },
    { id: 'api', name: 'API & Integrations', icon: <Key size={20} /> }
  ];

  const handleSave = () => {
    // Simulate saving
    setUnsavedChanges(false);
    // Show success message
  };

  const handleReset = () => {
    // Reset to original values
    setUnsavedChanges(false);
  };

  const renderProfileSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Full Name
          </label>
          <input
            type="text"
            value={profileData.name}
            onChange={(e) => {
              setProfileData({...profileData, name: e.target.value});
              setUnsavedChanges(true);
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Email Address
          </label>
          <input
            type="email"
            value={profileData.email}
            onChange={(e) => {
              setProfileData({...profileData, email: e.target.value});
              setUnsavedChanges(true);
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Organization
          </label>
          <input
            type="text"
            value={profileData.organization}
            onChange={(e) => {
              setProfileData({...profileData, organization: e.target.value});
              setUnsavedChanges(true);
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Job Title
          </label>
          <input
            type="text"
            value={profileData.title}
            onChange={(e) => {
              setProfileData({...profileData, title: e.target.value});
              setUnsavedChanges(true);
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Bio
        </label>
        <textarea
          value={profileData.bio}
          onChange={(e) => {
            setProfileData({...profileData, bio: e.target.value});
            setUnsavedChanges(true);
          }}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        />
      </div>
    </div>
  );

  const renderNotificationSettings = () => (
    <div className="space-y-6">
      {Object.entries(notifications).map(([key, value]) => (
        <div key={key} className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">
              {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {key === 'emailNotifications' && 'Receive email notifications for important updates'}
              {key === 'researchUpdates' && 'Get notified when your research projects have updates'}
              {key === 'weeklyDigest' && 'Receive a weekly summary of your research activity'}
              {key === 'collaborationInvites' && 'Get notified when someone invites you to collaborate'}
              {key === 'systemAlerts' && 'Receive alerts about system maintenance and updates'}
              {key === 'marketingEmails' && 'Receive promotional emails and feature announcements'}
            </p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={value}
              onChange={(e) => {
                setNotifications({...notifications, [key]: e.target.checked});
                setUnsavedChanges(true);
              }}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 dark:peer-focus:ring-indigo-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-indigo-600"></div>
          </label>
        </div>
      ))}
    </div>
  );

  const renderPrivacySettings = () => (
    <div className="space-y-6">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Profile Visibility
        </label>
        <select
          value={privacy.profileVisibility}
          onChange={(e) => {
            setPrivacy({...privacy, profileVisibility: e.target.value});
            setUnsavedChanges(true);
          }}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        >
          <option value="public">Public</option>
          <option value="collaborators">Collaborators Only</option>
          <option value="private">Private</option>
        </select>
      </div>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Research Visibility
        </label>
        <select
          value={privacy.researchVisibility}
          onChange={(e) => {
            setPrivacy({...privacy, researchVisibility: e.target.value});
            setUnsavedChanges(true);
          }}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        >
          <option value="public">Public</option>
          <option value="collaborators">Collaborators Only</option>
          <option value="private">Private</option>
        </select>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">Two-Factor Authentication</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Add an extra layer of security to your account</p>
          </div>
          <button
            onClick={() => {
              setPrivacy({...privacy, twoFactorAuth: !privacy.twoFactorAuth});
              setUnsavedChanges(true);
            }}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              privacy.twoFactorAuth 
                ? 'bg-red-600 text-white hover:bg-red-700' 
                : 'bg-indigo-600 text-white hover:bg-indigo-700'
            }`}
          >
            {privacy.twoFactorAuth ? 'Disable' : 'Enable'}
          </button>
        </div>
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-4">Theme</h3>
        <div className="grid grid-cols-3 gap-4">
          {[
            { value: 'light', label: 'Light', icon: <Sun size={20} /> },
            { value: 'dark', label: 'Dark', icon: <Moon size={20} /> },
            { value: 'system', label: 'System', icon: <Monitor size={20} /> }
          ].map((option) => (
            <button
              key={option.value}
              onClick={() => {
                setTheme(option.value as any);
                setUnsavedChanges(true);
              }}
              className={`p-4 rounded-lg border-2 transition-colors ${
                theme === option.value
                  ? 'border-indigo-600 bg-indigo-50 dark:bg-indigo-900/20'
                  : 'border-gray-300 dark:border-gray-600 hover:border-gray-400'
              }`}
            >
              <div className="flex flex-col items-center gap-2">
                {option.icon}
                <span className="text-sm font-medium">{option.label}</span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );

  const renderDataSettings = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Backup Frequency
          </label>
          <select
            value={dataSettings.backupFrequency}
            onChange={(e) => {
              setDataSettings({...dataSettings, backupFrequency: e.target.value});
              setUnsavedChanges(true);
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Data Retention
          </label>
          <select
            value={dataSettings.dataRetention}
            onChange={(e) => {
              setDataSettings({...dataSettings, dataRetention: e.target.value});
              setUnsavedChanges(true);
            }}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="1year">1 Year</option>
            <option value="2years">2 Years</option>
            <option value="5years">5 Years</option>
            <option value="forever">Forever</option>
          </select>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">Export Data</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Download all your research data</p>
          </div>
          <button className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
            <Download size={16} className="mr-2 inline" />
            Export
          </button>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-gray-900 dark:text-gray-100">Import Data</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Import research data from other platforms</p>
          </div>
          <button className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            <Upload size={16} className="mr-2 inline" />
            Import
          </button>
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-medium text-red-600 dark:text-red-400">Delete Account</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Permanently delete your account and all data</p>
          </div>
          <button className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors">
            <Trash2 size={16} className="mr-2 inline" />
            Delete
          </button>
        </div>
      </div>
    </div>
  );

  const renderAPISettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-4">API Keys</h3>
        <div className="space-y-4">
          <div className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="font-medium">Research API Key</span>
              <span className="text-xs text-green-600 bg-green-100 dark:bg-green-900/30 px-2 py-1 rounded">Active</span>
            </div>
            <div className="flex items-center gap-2">
              <code className="flex-1 p-2 bg-gray-100 dark:bg-gray-800 rounded text-sm font-mono">
                {showPassword ? 'rn_1234567890abcdef...' : '••••••••••••••••••••'}
              </code>
              <button
                onClick={() => setShowPassword(!showPassword)}
                className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
              >
                {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
              </button>
            </div>
          </div>
        </div>
        <button className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
          Generate New Key
        </button>
      </div>
    </div>
  );

  return (
    <PageTransition>
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex">
          {/* Sidebar */}
          <div className="w-64 border-r border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900">
            <div className="p-6">
              <h1 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-6">Settings</h1>
              <nav className="space-y-2">
                {settingsSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveSection(section.id as SettingsSection)}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === section.id
                        ? 'bg-indigo-50 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300'
                        : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                    }`}
                  >
                    {section.icon}
                    <span className="font-medium">{section.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-6">
              <div className="max-w-4xl">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                    {settingsSections.find(s => s.id === activeSection)?.name}
                  </h2>
                  {unsavedChanges && (
                    <div className="flex items-center gap-3">
                      <span className="text-sm text-yellow-600 dark:text-yellow-400">Unsaved changes</span>
                      <div className="flex gap-2">
                        <button
                          onClick={handleReset}
                          className="px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800"
                        >
                          <X size={14} className="mr-1 inline" />
                          Reset
                        </button>
                        <button
                          onClick={handleSave}
                          className="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700"
                        >
                          <Check size={14} className="mr-1 inline" />
                          Save
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                <GlassmorphicCard>
                  <div className="p-6">
                    {activeSection === 'profile' && renderProfileSettings()}
                    {activeSection === 'notifications' && renderNotificationSettings()}
                    {activeSection === 'privacy' && renderPrivacySettings()}
                    {activeSection === 'appearance' && renderAppearanceSettings()}
                    {activeSection === 'data' && renderDataSettings()}
                    {activeSection === 'api' && renderAPISettings()}
                  </div>
                </GlassmorphicCard>
              </div>
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
