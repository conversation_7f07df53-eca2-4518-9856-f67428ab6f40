import { useEffect, useState } from 'react';
import { motion } from 'framer-motion';
import { ArrowUpRight, BookOpen, Brain, Check, ChevronDown, Copy, Download, ExternalLink, FileText, FlaskConical, Globe, Newspaper, Shield, X } from 'lucide-react';
import { Research } from '../context/ResearchContext';

type ReportPreviewModalProps = {
  isOpen: boolean;
  onClose: () => void;
  research: Research;
};

export default function ReportPreviewModal({ isOpen, onClose, research }: ReportPreviewModalProps) {
  const [activeSection, setActiveSection] = useState('summary');
  const [copied, setCopied] = useState(false);
  const [loadingContent, setLoadingContent] = useState(false);
  const [expandedFindings, setExpandedFindings] = useState<string[]>([]);
  
  const isCompleted = research.status === 'completed';
  
  useEffect(() => {
    if (isOpen) {
      // Reset states when modal opens
      setActiveSection('summary');
      setCopied(false);
      
      // Simulate loading content if research is in progress
      if (!isCompleted) {
        setLoadingContent(true);
        const timer = setTimeout(() => {
          setLoadingContent(false);
        }, 2000);
        return () => clearTimeout(timer);
      }
    }
  }, [isOpen, isCompleted]);
  
  // Toggle expanded state for findings
  const toggleFinding = (findingId: string) => {
    if (expandedFindings.includes(findingId)) {
      setExpandedFindings(expandedFindings.filter(id => id !== findingId));
    } else {
      setExpandedFindings([...expandedFindings, findingId]);
    }
  };
  
  // Generate findings based on research title and sources
  const generateFindings = () => {
    const findings = [];
    
    // Finding 1: Based on academic sources if available
    if (research.sources.includes('Academic')) {
      findings.push({
        id: 'finding1',
        title: `Academic Research on ${research.title}`,
        summary: `Recent academic studies have shown significant advancements in ${research.title.toLowerCase()}, with multiple peer-reviewed papers highlighting the importance of continued research in this field.`,
        detail: `Multiple academic institutions have published research on ${research.title.toLowerCase()} in the past ${research.timeRange || '5'} years. The consensus among researchers points to remarkable progress, particularly in theoretical frameworks and methodological approaches. Universities including MIT, Stanford, and Oxford have dedicated research teams exploring various aspects of this topic, with their findings suggesting both practical applications and areas requiring further investigation.`,
        sources: research.credibleSourcesFound > 3 ? 3 : Math.max(1, Math.floor(research.credibleSourcesFound * 0.3)),
        confidence: 87
      });
    }
    
    // Finding 2: Based on industry sources if available
    if (research.sources.includes('Industry')) {
      findings.push({
        id: 'finding2',
        title: `Industry Applications of ${research.title}`,
        summary: `Leading industry players are actively implementing solutions related to ${research.title.toLowerCase()}, with a focus on practical applications and commercial viability.`,
        detail: `Industry reports and white papers indicate substantial investment in ${research.title.toLowerCase()} technologies. Companies are primarily focused on scalability, cost-effectiveness, and integration with existing systems. The market is projected to grow significantly over the next 3-5 years, with early adopters already reporting improved efficiency metrics and competitive advantages. Implementation challenges remain, particularly regarding standardization and regulatory compliance, though industry consortiums are actively addressing these concerns.`,
        sources: research.credibleSourcesFound > 4 ? 2 : Math.max(1, Math.floor(research.credibleSourcesFound * 0.2)),
        confidence: 82
      });
    }
    
    // Finding 3: Based on scientific sources if available
    if (research.sources.includes('Scientific')) {
      findings.push({
        id: 'finding3',
        title: `Scientific Validation of ${research.title}`,
        summary: `Scientific studies provide empirical evidence supporting key aspects of ${research.title.toLowerCase()}, with experimental data confirming theoretical predictions.`,
        detail: `Laboratory experiments and controlled studies have validated several fundamental hypotheses related to ${research.title.toLowerCase()}. The data shows statistically significant results across multiple test scenarios, with reproducibility confirmed by independent research teams. Quantitative analyses reveal correlation coefficients exceeding 0.75 in primary outcome measures, suggesting strong relationships between key variables. These findings align with theoretical models while also highlighting some unexpected phenomena that warrant further investigation.`,
        sources: research.credibleSourcesFound > 5 ? 4 : Math.max(1, Math.floor(research.credibleSourcesFound * 0.25)),
        confidence: 91
      });
    }
    
    // Finding 4: Based on news sources if available
    if (research.sources.includes('News')) {
      findings.push({
        id: 'finding4',
        title: `Current Developments in ${research.title}`,
        summary: `Recent news coverage indicates growing public and institutional interest in ${research.title.toLowerCase()}, with notable developments receiving media attention.`,
        detail: `Media analysis shows an upward trend in coverage of ${research.title.toLowerCase()} over the past ${research.timeRange || '5'} years, with a marked increase in the last 12 months. Major publications have featured in-depth articles examining both technological advancements and societal implications. Public perception appears generally positive, though concerns regarding ethical considerations and potential misuse have been raised by various stakeholders. Policy discussions are emerging at local and national levels, suggesting increased regulatory attention in the near future.`,
        sources: research.credibleSourcesFound > 4 ? 2 : Math.max(1, Math.floor(research.credibleSourcesFound * 0.15)),
        confidence: 75
      });
    }
    
    // Finding 5: Based on government sources if available
    if (research.sources.includes('Government')) {
      findings.push({
        id: 'finding5',
        title: `Policy Implications of ${research.title}`,
        summary: `Government agencies are developing regulatory frameworks and policy guidelines related to ${research.title.toLowerCase()}, indicating recognition of its importance.`,
        detail: `Official documents from multiple government entities reveal a coordinated approach to addressing ${research.title.toLowerCase()} through policy development. Public records indicate allocation of research funding and establishment of specialized committees to evaluate potential impacts. Regulatory considerations focus on balancing innovation with consumer protection and public safety. International cooperation is evident, with cross-border initiatives aimed at harmonizing standards and sharing best practices. Timeline projections suggest implementation of comprehensive policies within 18-24 months.`,
        sources: research.credibleSourcesFound > 6 ? 2 : Math.max(1, Math.floor(research.credibleSourcesFound * 0.1)),
        confidence: 79
      });
    }
    
    // Add a general finding if we have few specific ones
    if (findings.length < 3) {
      findings.push({
        id: 'finding-general',
        title: `General Analysis of ${research.title}`,
        summary: `Cross-disciplinary examination of ${research.title.toLowerCase()} reveals interconnected themes and complementary perspectives that enhance understanding of the subject.`,
        detail: `Integration of insights from multiple domains provides a comprehensive view of ${research.title.toLowerCase()}. Analysis of available data indicates consistent patterns across diverse sources, suggesting fundamental principles underlying various applications and implementations. Comparative evaluation of contradictory viewpoints highlights areas of consensus as well as remaining knowledge gaps. The synthesis of quantitative and qualitative information yields a nuanced understanding that accounts for both technical details and broader contextual factors.`,
        sources: Math.max(1, Math.floor(research.credibleSourcesFound * 0.2)),
        confidence: 83
      });
    }
    
    return findings;
  };
  
  // Generate executive summary based on research details
  const generateExecutiveSummary = () => {
    const depth = research.researchDepth === '1' ? 'preliminary' : research.researchDepth === '2' ? 'comprehensive' : 'extensive';
    const sourcesAnalysis = `${research.credibleSourcesFound} verified sources out of ${research.sourcesScanned} total sources analyzed`;
    const timeframe = research.timeRange === 'all' ? 'across all available time periods' : `from the past ${research.timeRange} years`;
    
    return `
This report presents a ${depth} analysis of ${research.title}, based on ${sourcesAnalysis} ${timeframe}. The research utilized AI-powered methodology to identify, verify, and synthesize information from multiple sources including ${research.sources.join(', ')} materials.

Key findings indicate significant developments in this field, with particular emphasis on ${research.sources.includes('Academic') ? 'academic research' : research.sources.includes('Industry') ? 'industry applications' : 'current developments'}. The analysis reveals both established consensus and emerging trends, providing a foundation for informed decision-making and strategic planning.

The verification process employed rigorous standards to ensure information quality, with particular attention to source credibility, publication date, and cross-referencing validation. This approach resulted in a credibility rate of ${(research.credibleSourcesFound / research.sourcesScanned * 100).toFixed(0)}%, providing high confidence in the conclusions presented.

${research.description ? `Additional context regarding ${research.description} has been incorporated throughout the analysis to address specific information needs.` : ''}
    `;
  };
  
  // Generate methodology description
  const generateMethodology = () => {
    return `
## Research Methodology

This research employed a multi-stage AI-powered approach to information gathering, verification, and synthesis:

### 1. Information Collection
- Systematic scanning of ${research.sourcesScanned} sources across ${research.sources.length} distinct categories
- Temporal scope limited to ${research.timeRange === 'all' ? 'all available time periods' : `the past ${research.timeRange} years`}
- Depth parameter set to ${research.researchDepth === '1' ? 'Basic' : research.researchDepth === '2' ? 'Standard' : 'Comprehensive'}

### 2. Source Verification
- Multi-factor credibility assessment including:
  - Publication reputation analysis
  - Author credential verification
  - Peer-review status determination
  - Citation network mapping
  - Cross-reference validation
- Resulting in ${research.credibleSourcesFound} verified sources (${(research.credibleSourcesFound / research.sourcesScanned * 100).toFixed(0)}% credibility rate)

### 3. Content Analysis
- Natural language processing to extract key concepts and relationships
- Semantic clustering to identify thematic patterns
- Sentiment analysis to evaluate perspective balance
- Contradiction detection to highlight areas of scholarly debate

### 4. Synthesis and Reporting
- Hierarchical organization of findings by confidence level
- Integration of quantitative metrics with qualitative insights
- Bias mitigation through multi-perspective representation
- Uncertainty acknowledgment where evidence is limited

This methodology was executed using the ${research.aiModel || 'GPT-4'} model, with all processing conducted in accordance with data privacy standards and information ethics guidelines.
    `;
  };
  
  const generateSourceList = () => {
    // Generate mock sources based on research parameters
    const sourceTypes = {
      Academic: {
        domains: ['sciencedirect.com', 'springer.com', 'nature.com', 'jstor.org', 'researchgate.net'],
        titles: ['Research on', 'Analysis of', 'Study of', 'Advances in'],
      },
      Scientific: {
        domains: ['science.org', 'pnas.org', 'cell.com', 'acs.org', 'ieee.org'],
        titles: ['Scientific approach to', 'Experimental results on', 'Laboratory findings for'],
      },
      News: {
        domains: ['nytimes.com', 'reuters.com', 'bbc.com', 'apnews.com', 'bloomberg.com'],
        titles: ['Report:', 'Analysis:', 'Special report:'],
      },
      Government: {
        domains: ['gov', 'europa.eu', 'un.org', 'who.int', 'nih.gov'],
        titles: ['Official report on', 'Government analysis of', 'Public data on'],
      },
      Industry: {
        domains: ['mckinsey.com', 'deloitte.com', 'gartner.com', 'forrester.com', 'pwc.com'],
        titles: ['Industry perspective on', 'Market analysis of', 'Sector report:'],
      },
      Web: {
        domains: ['medium.com', 'wordpress.com', 'blogspot.com', 'substack.com', 'webmd.com'],
        titles: ['Guide to', 'Overview of', 'Perspective on'],
      }
    };
    
    const sources = [];
    
    // Generate sources for each selected source type
    for (const sourceType of research.sources) {
      if (sourceTypes[sourceType as keyof typeof sourceTypes]) {
        const sourceInfo = sourceTypes[sourceType as keyof typeof sourceTypes];
        const count = Math.max(1, Math.floor(research.credibleSourcesFound / research.sources.length));
        
        for (let i = 0; i < count; i++) {
          const titlePrefix = sourceInfo.titles[Math.floor(Math.random() * sourceInfo.titles.length)];
          const domain = sourceInfo.domains[Math.floor(Math.random() * sourceInfo.domains.length)];
          
          // Generate author names
          const firstNames = ['John', 'Sarah', 'Michael', 'Emily', 'David', 'Jennifer', 'Robert', 'Lisa'];
          const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis'];
          const author = `${firstNames[Math.floor(Math.random() * firstNames.length)]} ${lastNames[Math.floor(Math.random() * lastNames.length)]}`;
          
          // Generate publication year based on time range
          const currentYear = new Date().getFullYear();
          const yearRange = research.timeRange === 'all' ? 15 : parseInt(research.timeRange || '5');
          const year = currentYear - Math.floor(Math.random() * yearRange);
          
          sources.push({
            title: `${titlePrefix} ${research.title}`,
            author,
            year,
            source: domain,
            type: sourceType,
            url: `https://www.${domain}/article-${i+1}`,
          });
        }
      }
    }
    
    return sources;
  };
  
  const findings = generateFindings();
  const executiveSummary = generateExecutiveSummary();
  const methodology = generateMethodology();
  const sourceList = generateSourceList();
  
  // Create a full report text for copying
  const fullReportText = `
# Research Report: ${research.title}

## Executive Summary
${executiveSummary.trim()}

## Key Findings
${findings.map(finding => `
### ${finding.title}
${finding.summary}

${finding.detail}

**Confidence Level:** ${finding.confidence}%
**Sources:** ${finding.sources} verified references
`).join('\n')}

${methodology}

## References
${sourceList.map((source, index) => `
[${index + 1}] ${source.author} (${source.year}). "${source.title}". ${source.source}
`).join('')}

---
Report generated on ${new Date().toLocaleDateString()} using AI-powered research assistant.
Source verification rate: ${(research.credibleSourcesFound / research.sourcesScanned * 100).toFixed(0)}%
  `;
  
  const handleCopyReport = () => {
    navigator.clipboard.writeText(fullReportText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };
  
  const handleDownloadReport = () => {
    const blob = new Blob([fullReportText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${research.title.replace(/\s+/g, '_')}_Report.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };
  
  if (!isOpen) return null;
  
  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <motion.div
        className="bg-white rounded-xl w-full max-w-4xl max-h-[90vh] flex flex-col"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-100 rounded-lg text-purple-600">
              <FileText size={22} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">
                {isCompleted ? 'Research Report' : 'Report Preview'}
              </h2>
              <p className="text-sm text-gray-500">{research.title}</p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>
        
        <div className="border-b border-gray-200">
          <div className="flex">
            <button
              onClick={() => setActiveSection('summary')}
              className={`py-3 px-4 text-sm font-medium ${
                activeSection === 'summary' 
                  ? 'text-purple-700 border-b-2 border-purple-500' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Executive Summary
            </button>
            <button
              onClick={() => setActiveSection('findings')}
              className={`py-3 px-4 text-sm font-medium ${
                activeSection === 'findings' 
                  ? 'text-purple-700 border-b-2 border-purple-500' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Key Findings
            </button>
            <button
              onClick={() => setActiveSection('methodology')}
              className={`py-3 px-4 text-sm font-medium ${
                activeSection === 'methodology' 
                  ? 'text-purple-700 border-b-2 border-purple-500' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Methodology
            </button>
            <button
              onClick={() => setActiveSection('sources')}
              className={`py-3 px-4 text-sm font-medium ${
                activeSection === 'sources' 
                  ? 'text-purple-700 border-b-2 border-purple-500' 
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              Sources
            </button>
          </div>
        </div>
        
        <div className="flex-1 overflow-y-auto p-6">
          {loadingContent ? (
            <div className="flex flex-col items-center justify-center h-full py-8">
              <div className="w-12 h-12 rounded-full border-4 border-gray-200 border-t-purple-600 animate-spin mb-4"></div>
              <p className="text-gray-600">Generating report preview...</p>
              <p className="text-sm text-gray-500 mt-1">AI is analyzing {research.sourcesScanned} sources</p>
            </div>
          ) : (
            <>
              {/* Executive Summary */}
              {activeSection === 'summary' && (
                <div className="prose prose-sm max-w-none">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="p-1.5 bg-purple-100 rounded-md text-purple-600">
                      <FileText size={16} />
                    </div>
                    <h3 className="font-semibold text-gray-900 m-0">Executive Summary</h3>
                  </div>
                  
                  {isCompleted ? (
                    <div>
                      {executiveSummary.split('\n\n').map((paragraph, index) => (
                        <p key={index} className="mb-4 text-gray-700 whitespace-pre-line">{paragraph}</p>
                      ))}
                    </div>
                  ) : (
                    <div className="p-4 border border-dashed border-yellow-300 bg-yellow-50 rounded-lg">
                      <p className="text-yellow-800 text-sm">
                        <strong>Preview Mode:</strong> This is a preliminary summary based on {research.progress}% of the research completed so far. The final report will be more comprehensive.
                      </p>
                    </div>
                  )}
                  
                  <div className="flex gap-2 mt-6">
                    <div className="px-3 py-1.5 bg-green-100 text-green-700 rounded-lg text-xs flex items-center gap-1.5">
                      <Check size={12} />
                      <span>AI-Verified Content</span>
                    </div>
                    <div className="px-3 py-1.5 bg-blue-100 text-blue-700 rounded-lg text-xs flex items-center gap-1.5">
                      <Shield size={12} />
                      <span>{research.credibleSourcesFound} Credible Sources</span>
                    </div>
                    <div className="px-3 py-1.5 bg-purple-100 text-purple-700 rounded-lg text-xs flex items-center gap-1.5">
                      <Brain size={12} />
                      <span>{research.aiModel || 'GPT-4'}</span>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Key Findings */}
              {activeSection === 'findings' && (
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <div className="p-1.5 bg-blue-100 rounded-md text-blue-600">
                      <FlaskConical size={16} />
                    </div>
                    <h3 className="font-semibold text-gray-900">Key Findings</h3>
                  </div>
                  
                  {findings.length > 0 ? (
                    <div className="space-y-4">
                      {findings.map((finding) => (
                        <div 
                          key={finding.id} 
                          className="border border-gray-200 rounded-lg overflow-hidden"
                        >
                          <div 
                            className="p-4 bg-white flex justify-between items-start cursor-pointer hover:bg-gray-50"
                            onClick={() => toggleFinding(finding.id)}
                          >
                            <div>
                              <h4 className="font-medium text-gray-800">{finding.title}</h4>
                              <p className="text-sm text-gray-600 mt-1">{finding.summary}</p>
                              <div className="flex items-center gap-2 mt-2">
                                <div className="flex items-center gap-1 text-xs text-gray-500">
                                  <Shield size={12} className="text-green-500" />
                                  <span>{finding.sources} sources</span>
                                </div>
                                <div className="flex items-center gap-1 text-xs text-gray-500">
                                  <Check size={12} className="text-blue-500" />
                                  <span>{finding.confidence}% confidence</span>
                                </div>
                              </div>
                            </div>
                            <ChevronDown 
                              size={18} 
                              className={`text-gray-400 transition-transform ${
                                expandedFindings.includes(finding.id) ? 'transform rotate-180' : ''
                              }`} 
                            />
                          </div>
                          
                          {expandedFindings.includes(finding.id) && (
                            <div className="p-4 border-t border-gray-100 bg-gray-50">
                              <p className="text-gray-700 text-sm whitespace-pre-line">{finding.detail}</p>
                              <div className="mt-3 pt-3 border-t border-gray-200">
                                <h5 className="text-xs font-medium text-gray-500 mb-2">SUPPORTING EVIDENCE</h5>
                                <div className="space-y-2">
                                  {sourceList
                                    .filter(source => 
                                      source.type === (
                                        finding.id === 'finding1' ? 'Academic' : 
                                        finding.id === 'finding2' ? 'Industry' : 
                                        finding.id === 'finding3' ? 'Scientific' : 
                                        finding.id === 'finding4' ? 'News' : 
                                        finding.id === 'finding5' ? 'Government' : 
                                        null
                                      )
                                    )
                                    .slice(0, finding.sources)
                                    .map((source, index) => (
                                      <div key={index} className="flex items-start gap-2">
                                        <div className="p-1 bg-blue-100 rounded-md text-blue-600 mt-0.5">
                                          {source.type === 'Academic' || source.type === 'Scientific' ? (
                                            <BookOpen size={12} />
                                          ) : source.type === 'News' ? (
                                            <Newspaper size={12} />
                                          ) : (
                                            <Globe size={12} />
                                          )}
                                        </div>
                                        <div className="flex-1">
                                          <p className="text-xs text-gray-700 font-medium">{source.title}</p>
                                          <p className="text-xs text-gray-500">{source.author}, {source.year}</p>
                                        </div>
                                        <a href={source.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:text-blue-800">
                                          <ExternalLink size={12} />
                                        </a>
                                      </div>
                                    ))
                                  }
                                </div>
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500">No findings available yet.</p>
                    </div>
                  )}
                  
                  {!isCompleted && (
                    <div className="mt-4 p-4 border border-dashed border-yellow-300 bg-yellow-50 rounded-lg">
                      <p className="text-yellow-800 text-sm">
                        <strong>Preview Mode:</strong> Additional findings will be available when research is complete. Current findings are based on {research.progress}% of analyzed sources.
                      </p>
                    </div>
                  )}
                </div>
              )}
              
              {/* Methodology */}
              {activeSection === 'methodology' && (
                <div className="prose prose-sm max-w-none">
                  <div className="flex items-center gap-2 mb-4">
                    <div className="p-1.5 bg-indigo-100 rounded-md text-indigo-600">
                      <Brain size={16} />
                    </div>
                    <h3 className="font-semibold text-gray-900 m-0">Research Methodology</h3>
                  </div>
                  
                  <div className="whitespace-pre-line">
                    {methodology}
                  </div>
                  
                  {!isCompleted && (
                    <div className="mt-4 p-4 border border-dashed border-yellow-300 bg-yellow-50 rounded-lg">
                      <p className="text-yellow-800 text-sm">
                        <strong>Preview Mode:</strong> The methodology description reflects the current research approach and will be updated as the research progresses.
                      </p>
                    </div>
                  )}
                </div>
              )}
              
              {/* Sources */}
              {activeSection === 'sources' && (
                <div>
                  <div className="flex items-center gap-2 mb-4">
                    <div className="p-1.5 bg-green-100 rounded-md text-green-600">
                      <Shield size={16} />
                    </div>
                    <h3 className="font-semibold text-gray-900">Verified Sources</h3>
                  </div>
                  
                  <div className="mb-4">
                    <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200">
                      <span className="text-sm text-gray-700 font-medium">
                        {research.credibleSourcesFound} verified sources out of {research.sourcesScanned} analyzed
                      </span>
                      <span className="px-2 py-0.5 bg-green-100 text-green-700 rounded text-xs">
                        {(research.credibleSourcesFound / research.sourcesScanned * 100).toFixed(0)}% verification rate
                      </span>
                    </div>
                  </div>
                  
                  {sourceList.length > 0 ? (
                    <div className="space-y-3 mt-6">
                      {sourceList.map((source, index) => (
                        <div key={index} className="p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                          <div className="flex items-start gap-3">
                            <div className="p-1.5 rounded-md mt-0.5 flex-shrink-0">
                              {source.type === 'Academic' ? (
                                <div className="p-1 bg-blue-100 rounded-md text-blue-600">
                                  <BookOpen size={16} />
                                </div>
                              ) : source.type === 'Scientific' ? (
                                <div className="p-1 bg-indigo-100 rounded-md text-indigo-600">
                                  <FlaskConical size={16} />
                                </div>
                              ) : source.type === 'News' ? (
                                <div className="p-1 bg-purple-100 rounded-md text-purple-600">
                                  <Newspaper size={16} />
                                </div>
                              ) : (
                                <div className="p-1 bg-green-100 rounded-md text-green-600">
                                  <Globe size={16} />
                                </div>
                              )}
                            </div>
                            <div className="flex-1">
                              <h4 className="text-sm font-medium text-gray-800">{source.title}</h4>
                              <p className="text-xs text-gray-600 mt-0.5">
                                {source.author} ({source.year}) • {source.source}
                              </p>
                              <div className="flex items-center gap-1 mt-1.5">
                                <span className="px-2 py-0.5 bg-gray-100 text-gray-700 rounded text-xs">
                                  {source.type}
                                </span>
                              </div>
                            </div>
                            <a 
                              href={source.url}
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="text-blue-600 hover:text-blue-800 p-1"
                            >
                              <ArrowUpRight size={16} />
                            </a>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500">No verified sources available yet.</p>
                    </div>
                  )}
                  
                  {!isCompleted && (
                    <div className="mt-4 p-4 border border-dashed border-yellow-300 bg-yellow-50 rounded-lg">
                      <p className="text-yellow-800 text-sm">
                        <strong>Preview Mode:</strong> This is a partial list of sources. More sources will be verified as the research progresses.
                      </p>
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
        
        <div className="border-t border-gray-200 p-4 flex justify-between items-center bg-gray-50 rounded-b-xl">
          <div className="text-sm text-gray-500 flex items-center gap-2">
            <div className="p-1 bg-purple-100 rounded-full text-purple-600">
              <Brain size={14} />
            </div>
            <span>Generated by {research.aiModel || 'GPT-4'}</span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={handleCopyReport}
              className="px-3 py-1.5 text-sm text-gray-700 font-medium rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors flex items-center gap-1.5"
            >
              <Copy size={14} />
              <span>{copied ? 'Copied!' : 'Copy'}</span>
            </button>
            <button
              onClick={handleDownloadReport}
              className="px-3 py-1.5 text-sm text-white font-medium rounded-lg bg-gradient-to-r from-indigo-600 to-purple-600 hover:shadow-md transition-shadow flex items-center gap-1.5"
            >
              <Download size={14} />
              <span>Download</span>
            </button>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
}
