import { Brain, Check, Clock, Database, FileText, Shield } from 'lucide-react';
import { motion } from 'framer-motion';
import { Research } from '../context/ResearchContext';

type ActivityFeedProps = {
  inProgressResearch: Research[];
  completedResearch: Research[];
}

export default function ActivityFeed({ inProgressResearch, completedResearch }: ActivityFeedProps) {
  // Generate activities based on in-progress and completed research
  const generateActivities = () => {
    const activities = [];
    
    // Activities from in-progress research
    inProgressResearch.forEach(research => {
      // Source scanning activity
      activities.push({
        id: `scan-${research.id}`,
        title: `Scanning sources for "${research.title}"`,
        description: `${research.sourcesScanned} sources analyzed so far`,
        time: '10 minutes ago',
        icon: <Database size={16} className="text-indigo-500" />,
        status: 'in-progress'
      });
      
      // Source verification if found
      if (research.credibleSourcesFound > 0) {
        activities.push({
          id: `verify-${research.id}`,
          title: `Verified sources for "${research.title}"`,
          description: `${research.credibleSourcesFound} credible sources identified`,
          time: '8 minutes ago',
          icon: <Shield size={16} className="text-green-500" />,
          status: 'completed'
        });
      }
    });
    
    // Activities from completed research
    completedResearch.slice(0, 2).forEach(research => {
      activities.push({
        id: `complete-${research.id}`,
        title: `Completed research on "${research.title}"`,
        description: `Report generated with ${research.credibleSourcesFound} verified sources`,
        time: `on ${research.completedDate}`,
        icon: <Check size={16} className="text-green-500" />,
        status: 'completed'
      });
    });
    
    // Sort activities by recency (using id as a proxy since we don't have real timestamps)
    return activities.sort((a, b) => b.id.localeCompare(a.id)).slice(0, 6);
  };
  
  const activities = generateActivities();
  
  return (
    <div className="space-y-4">
      {activities.length > 0 ? (
        <div className="space-y-4">
          {activities.map((activity) => (
            <motion.div 
              key={activity.id}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="flex gap-3"
            >
              <div className="flex-shrink-0 mt-0.5">
                <div className="p-1.5 rounded-full bg-gray-100">
                  {activity.icon}
                </div>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-800">{activity.title}</h4>
                <p className="text-xs text-gray-600 mt-0.5">{activity.description}</p>
                <div className="flex items-center gap-2 mt-1">
                  <Clock size={12} className="text-gray-400" />
                  <span className="text-xs text-gray-500">{activity.time}</span>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-gray-500">No recent activity.</p>
          <p className="text-sm text-gray-400 mt-1">Start a new research to see activity here</p>
        </div>
      )}
    </div>
  );
}
