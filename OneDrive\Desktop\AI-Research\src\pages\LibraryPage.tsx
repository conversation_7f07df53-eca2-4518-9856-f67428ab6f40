import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  BookOpen, 
  Search, 
  Filter, 
  Plus, 
  Star, 
  Download, 
  Eye, 
  Bookmark,
  Tag,
  Calendar,
  FileText,
  Link,
  MoreVertical,
  Grid3X3,
  List
} from 'lucide-react';
import GlassmorphicCard from '../components/GlassmorphicCard';
import PageTransition from '../components/PageTransition';

type LibraryItem = {
  id: number;
  title: string;
  authors: string[];
  type: 'paper' | 'book' | 'article' | 'report' | 'dataset';
  description: string;
  tags: string[];
  dateAdded: string;
  datePublished: string;
  rating: number;
  isBookmarked: boolean;
  url?: string;
  downloadUrl?: string;
  pages?: number;
  citations?: number;
  journal?: string;
  category: string;
};

type ViewMode = 'grid' | 'list';
type FilterType = 'all' | 'papers' | 'books' | 'articles' | 'reports' | 'datasets';

export default function LibraryPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState<FilterType>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Mock library data
  const [libraryItems] = useState<LibraryItem[]>([
    {
      id: 1,
      title: "Attention Is All You Need",
      authors: ["Vaswani, A.", "Shazeer, N.", "Parmar, N."],
      type: "paper",
      description: "The paper that introduced the Transformer architecture, revolutionizing natural language processing.",
      tags: ["Transformers", "NLP", "Deep Learning", "Attention"],
      dateAdded: "2024-01-15",
      datePublished: "2017-06-12",
      rating: 5,
      isBookmarked: true,
      pages: 15,
      citations: 45000,
      journal: "NIPS 2017",
      category: "Machine Learning"
    },
    {
      id: 2,
      title: "Deep Learning",
      authors: ["Goodfellow, I.", "Bengio, Y.", "Courville, A."],
      type: "book",
      description: "Comprehensive textbook on deep learning covering mathematical foundations and practical applications.",
      tags: ["Deep Learning", "Neural Networks", "AI", "Textbook"],
      dateAdded: "2024-01-10",
      datePublished: "2016-11-18",
      rating: 5,
      isBookmarked: true,
      pages: 800,
      category: "Machine Learning"
    },
    {
      id: 3,
      title: "Climate Change and AI: Recommendations for Government Action",
      authors: ["Climate Change AI"],
      type: "report",
      description: "Policy recommendations for leveraging AI to address climate change challenges.",
      tags: ["Climate Change", "AI Policy", "Sustainability", "Government"],
      dateAdded: "2024-01-08",
      datePublished: "2023-12-01",
      rating: 4,
      isBookmarked: false,
      pages: 45,
      category: "Climate Science"
    },
    {
      id: 4,
      title: "ImageNet Large Scale Visual Recognition Dataset",
      authors: ["Deng, J.", "Dong, W.", "Socher, R."],
      type: "dataset",
      description: "Large-scale dataset for visual recognition research with over 14 million images.",
      tags: ["Computer Vision", "Dataset", "Image Classification"],
      dateAdded: "2024-01-05",
      datePublished: "2009-06-20",
      rating: 5,
      isBookmarked: true,
      category: "Computer Vision"
    },
    {
      id: 5,
      title: "The Ethics of Artificial Intelligence",
      authors: ["Russell, S.", "Norvig, P."],
      type: "article",
      description: "Comprehensive analysis of ethical considerations in AI development and deployment.",
      tags: ["AI Ethics", "Philosophy", "Technology Policy"],
      dateAdded: "2024-01-03",
      datePublished: "2023-09-15",
      rating: 4,
      isBookmarked: false,
      journal: "Nature AI",
      category: "AI Ethics"
    }
  ]);

  const categories = ['all', ...Array.from(new Set(libraryItems.map(item => item.category)))];

  const filteredItems = libraryItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.authors.some(author => author.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFilter = filter === 'all' || item.type === filter.slice(0, -1);
    const matchesCategory = selectedCategory === 'all' || item.category === selectedCategory;
    
    return matchesSearch && matchesFilter && matchesCategory;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'paper':
        return <FileText size={16} className="text-blue-500" />;
      case 'book':
        return <BookOpen size={16} className="text-green-500" />;
      case 'article':
        return <FileText size={16} className="text-purple-500" />;
      case 'report':
        return <FileText size={16} className="text-orange-500" />;
      case 'dataset':
        return <FileText size={16} className="text-red-500" />;
      default:
        return <FileText size={16} className="text-gray-500" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'paper':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'book':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'article':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'report':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'dataset':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={14}
        className={i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  return (
    <PageTransition>
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Library</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Your collection of research papers, books, and resources
                </p>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 ${viewMode === 'grid' ? 'bg-indigo-600 text-white' : 'text-gray-600 dark:text-gray-400'}`}
                  >
                    <Grid3X3 size={18} />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 ${viewMode === 'list' ? 'bg-indigo-600 text-white' : 'text-gray-600 dark:text-gray-400'}`}
                  >
                    <List size={18} />
                  </button>
                </div>
                <motion.button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Plus size={18} className="mr-2 inline" />
                  Add Item
                </motion.button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search library..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value as FilterType)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Types</option>
                  <option value="papers">Papers</option>
                  <option value="books">Books</option>
                  <option value="articles">Articles</option>
                  <option value="reports">Reports</option>
                  <option value="datasets">Datasets</option>
                </select>
                
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* Library Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {filteredItems.length === 0 ? (
              <div className="text-center py-12">
                <BookOpen size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No items found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {searchTerm ? 'Try adjusting your search terms' : 'Add your first research item to get started'}
                </p>
              </div>
            ) : viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredItems.map((item, index) => (
                  <GlassmorphicCard key={item.id} delay={index}>
                    <div className="p-5">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {getTypeIcon(item.type)}
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(item.type)}`}>
                            {item.type}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <button className={`p-1 rounded ${item.isBookmarked ? 'text-yellow-500' : 'text-gray-400'}`}>
                            <Bookmark size={16} className={item.isBookmarked ? 'fill-current' : ''} />
                          </button>
                          <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                            <MoreVertical size={16} className="text-gray-400" />
                          </button>
                        </div>
                      </div>

                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                        {item.title}
                      </h3>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {item.authors.join(', ')}
                      </p>

                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {item.description}
                      </p>

                      <div className="flex items-center gap-1 mb-3">
                        {renderStars(item.rating)}
                        <span className="text-xs text-gray-500 ml-1">({item.rating}/5)</span>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-3">
                        {item.tags.slice(0, 3).map(tag => (
                          <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded">
                            {tag}
                          </span>
                        ))}
                        {item.tags.length > 3 && (
                          <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded">
                            +{item.tags.length - 3}
                          </span>
                        )}
                      </div>

                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-4">
                        <div className="flex justify-between">
                          <span>Added: {new Date(item.dateAdded).toLocaleDateString()}</span>
                          {item.pages && <span>{item.pages} pages</span>}
                        </div>
                        {item.citations && (
                          <div className="mt-1">
                            <span>{item.citations.toLocaleString()} citations</span>
                          </div>
                        )}
                      </div>

                      <div className="flex gap-2">
                        <button className="flex-1 px-3 py-2 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
                          <Eye size={14} className="mr-1 inline" />
                          View
                        </button>
                        {item.downloadUrl && (
                          <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <Download size={14} />
                          </button>
                        )}
                        {item.url && (
                          <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                            <Link size={14} />
                          </button>
                        )}
                      </div>
                    </div>
                  </GlassmorphicCard>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {filteredItems.map((item, index) => (
                  <motion.div
                    key={item.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          {getTypeIcon(item.type)}
                          <h3 className="font-semibold text-gray-900 dark:text-gray-100">{item.title}</h3>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(item.type)}`}>
                            {item.type}
                          </span>
                          {item.isBookmarked && (
                            <Bookmark size={16} className="text-yellow-500 fill-current" />
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {item.authors.join(', ')}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {item.description}
                        </p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>Added: {new Date(item.dateAdded).toLocaleDateString()}</span>
                          {item.pages && <span>{item.pages} pages</span>}
                          {item.citations && <span>{item.citations.toLocaleString()} citations</span>}
                          <div className="flex items-center gap-1">
                            {renderStars(item.rating)}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <button className="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700">
                          View
                        </button>
                        <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                          <MoreVertical size={16} className="text-gray-400" />
                        </button>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
