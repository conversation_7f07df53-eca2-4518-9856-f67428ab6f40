{"name": "mocha-app", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^2.2.4", "daisyui": "^5.0.43", "framer-motion": "^12.20.5", "lucide-react": "^0.510.0", "react": "19.0.0", "react-dom": "19.0.0", "react-intersection-observer": "^9.16.0", "react-router-dom": "^7.6.3", "recharts": "^3.0.2"}, "devDependencies": {"@getmocha/vite-plugins": "latest", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.20", "globals": "^15.9.0", "postcss": "^8.4.47", "tailwindcss": "^3.4.14", "typescript": "^5.5.3", "vite": "^6.2.1"}}