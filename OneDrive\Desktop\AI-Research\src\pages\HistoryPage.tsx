import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Clock, 
  Search, 
  Filter, 
  Calendar, 
  FileText, 
  Download, 
  Eye, 
  Trash2,
  MoreVertical,
  CheckCircle,
  XCircle,
  Pause,
  Play,
  RotateCcw,
  TrendingUp,
  Activity
} from 'lucide-react';
import { useResearch } from '../context/ResearchContext';
import GlassmorphicCard from '../components/GlassmorphicCard';
import PageTransition from '../components/PageTransition';

type HistoryFilter = 'all' | 'completed' | 'in-progress' | 'paused' | 'cancelled';
type TimeFilter = 'all' | 'today' | 'week' | 'month' | 'year';
type ViewMode = 'timeline' | 'grid';

type ActivityLog = {
  id: number;
  researchId: number;
  action: string;
  description: string;
  timestamp: string;
  type: 'created' | 'updated' | 'completed' | 'paused' | 'resumed' | 'deleted';
};

export default function HistoryPage() {
  const { researches } = useResearch();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<HistoryFilter>('all');
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('all');
  const [viewMode, setViewMode] = useState<ViewMode>('timeline');

  // Generate activity logs from research data
  const generateActivityLogs = (): ActivityLog[] => {
    const logs: ActivityLog[] = [];
    let logId = 1;

    researches.forEach(research => {
      // Created log
      logs.push({
        id: logId++,
        researchId: research.id,
        action: 'Research Created',
        description: `Started research on "${research.title}"`,
        timestamp: research.createdAt,
        type: 'created'
      });

      // Progress updates (simulate some)
      if (research.progress && research.progress > 0) {
        const progressSteps = [25, 50, 75];
        progressSteps.forEach(step => {
          if (research.progress! >= step) {
            const progressDate = new Date(research.createdAt);
            progressDate.setHours(progressDate.getHours() + step);
            logs.push({
              id: logId++,
              researchId: research.id,
              action: `Progress Update`,
              description: `Research progress reached ${step}% - ${research.sourcesScanned} sources analyzed`,
              timestamp: progressDate.toISOString(),
              type: 'updated'
            });
          }
        });
      }

      // Status changes
      if (research.status === 'completed' && research.completedDate) {
        logs.push({
          id: logId++,
          researchId: research.id,
          action: 'Research Completed',
          description: `Completed research with ${research.credibleSourcesFound} credible sources found`,
          timestamp: research.completedDate,
          type: 'completed'
        });
      } else if (research.status === 'paused') {
        const pauseDate = new Date(research.createdAt);
        pauseDate.setHours(pauseDate.getHours() + 12);
        logs.push({
          id: logId++,
          researchId: research.id,
          action: 'Research Paused',
          description: `Research paused at ${research.progress}% completion`,
          timestamp: pauseDate.toISOString(),
          type: 'paused'
        });
      }
    });

    return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  };

  const [activityLogs] = useState(generateActivityLogs());

  const filterByTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    
    switch (timeFilter) {
      case 'today':
        return date.toDateString() === now.toDateString();
      case 'week':
        const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        return date >= weekAgo;
      case 'month':
        const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        return date >= monthAgo;
      case 'year':
        const yearAgo = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        return date >= yearAgo;
      default:
        return true;
    }
  };

  const filteredResearches = researches.filter(research => {
    const matchesSearch = research.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         research.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || research.status === statusFilter;
    const matchesTime = filterByTime(research.createdAt);
    
    return matchesSearch && matchesStatus && matchesTime;
  });

  const filteredLogs = activityLogs.filter(log => {
    const research = researches.find(r => r.id === log.researchId);
    if (!research) return false;
    
    const matchesSearch = research.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         log.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || research.status === statusFilter;
    const matchesTime = filterByTime(log.timestamp);
    
    return matchesSearch && matchesStatus && matchesTime;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'in-progress':
        return <Play size={16} className="text-blue-500" />;
      case 'paused':
        return <Pause size={16} className="text-yellow-500" />;
      default:
        return <XCircle size={16} className="text-gray-500" />;
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'created':
        return <Play size={16} className="text-green-500" />;
      case 'updated':
        return <TrendingUp size={16} className="text-blue-500" />;
      case 'completed':
        return <CheckCircle size={16} className="text-green-500" />;
      case 'paused':
        return <Pause size={16} className="text-yellow-500" />;
      case 'resumed':
        return <Play size={16} className="text-blue-500" />;
      case 'deleted':
        return <Trash2 size={16} className="text-red-500" />;
      default:
        return <Activity size={16} className="text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'paused':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    return date.toLocaleDateString();
  };

  return (
    <PageTransition>
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Research History</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Track your research activities and progress over time
                </p>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex border border-gray-300 dark:border-gray-600 rounded-lg">
                  <button
                    onClick={() => setViewMode('timeline')}
                    className={`px-3 py-2 text-sm ${viewMode === 'timeline' ? 'bg-indigo-600 text-white' : 'text-gray-600 dark:text-gray-400'}`}
                  >
                    Timeline
                  </button>
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`px-3 py-2 text-sm ${viewMode === 'grid' ? 'bg-indigo-600 text-white' : 'text-gray-600 dark:text-gray-400'}`}
                  >
                    Grid
                  </button>
                </div>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search research history..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value as HistoryFilter)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Status</option>
                  <option value="completed">Completed</option>
                  <option value="in-progress">In Progress</option>
                  <option value="paused">Paused</option>
                </select>
                
                <select
                  value={timeFilter}
                  onChange={(e) => setTimeFilter(e.target.value as TimeFilter)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Time</option>
                  <option value="today">Today</option>
                  <option value="week">This Week</option>
                  <option value="month">This Month</option>
                  <option value="year">This Year</option>
                </select>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6">
            {viewMode === 'timeline' ? (
              <div className="space-y-6">
                {filteredLogs.length === 0 ? (
                  <div className="text-center py-12">
                    <Clock size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No activity found</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {searchTerm ? 'Try adjusting your search terms' : 'Start a research project to see activity here'}
                    </p>
                  </div>
                ) : (
                  <div className="relative">
                    {/* Timeline line */}
                    <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>
                    
                    {filteredLogs.map((log, index) => {
                      const research = researches.find(r => r.id === log.researchId);
                      if (!research) return null;
                      
                      return (
                        <motion.div
                          key={log.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="relative flex items-start gap-4 pb-6"
                        >
                          {/* Timeline dot */}
                          <div className="relative z-10 flex items-center justify-center w-8 h-8 bg-white dark:bg-gray-900 border-2 border-gray-200 dark:border-gray-700 rounded-full">
                            {getActivityIcon(log.type)}
                          </div>
                          
                          {/* Content */}
                          <div className="flex-1 min-w-0">
                            <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4 shadow-sm">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h3 className="font-medium text-gray-900 dark:text-gray-100">{log.action}</h3>
                                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">{log.description}</p>
                                </div>
                                <span className="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-4">
                                  {formatTimeAgo(log.timestamp)}
                                </span>
                              </div>
                              
                              <div className="flex items-center gap-2 text-sm">
                                <span className="text-gray-600 dark:text-gray-400">Research:</span>
                                <span className="font-medium text-gray-900 dark:text-gray-100">{research.title}</span>
                                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(research.status)}`}>
                                  {research.status}
                                </span>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </div>
            ) : (
              <div>
                {filteredResearches.length === 0 ? (
                  <div className="text-center py-12">
                    <FileText size={48} className="mx-auto text-gray-400 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No research found</h3>
                    <p className="text-gray-600 dark:text-gray-400">
                      {searchTerm ? 'Try adjusting your search terms' : 'Start your first research project'}
                    </p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredResearches.map((research, index) => (
                      <GlassmorphicCard key={research.id} delay={index}>
                        <div className="p-5">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-center gap-2">
                              {getStatusIcon(research.status)}
                              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(research.status)}`}>
                                {research.status}
                              </span>
                            </div>
                            <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                              <MoreVertical size={16} className="text-gray-400" />
                            </button>
                          </div>

                          <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                            {research.title}
                          </h3>
                          
                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                            {research.description}
                          </p>

                          <div className="text-xs text-gray-500 dark:text-gray-400 mb-4 space-y-1">
                            <div className="flex justify-between">
                              <span>Created: {new Date(research.createdAt).toLocaleDateString()}</span>
                              {research.progress && <span>{research.progress}% complete</span>}
                            </div>
                            <div className="flex justify-between">
                              <span>Sources: {research.sourcesScanned}</span>
                              <span>Credible: {research.credibleSourcesFound}</span>
                            </div>
                            {research.completedDate && (
                              <div>
                                <span>Completed: {new Date(research.completedDate).toLocaleDateString()}</span>
                              </div>
                            )}
                          </div>

                          <div className="flex gap-2">
                            <button className="flex-1 px-3 py-2 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
                              <Eye size={14} className="mr-1 inline" />
                              View
                            </button>
                            {research.status === 'completed' && (
                              <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <Download size={14} />
                              </button>
                            )}
                            {research.status === 'paused' && (
                              <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                                <RotateCcw size={14} />
                              </button>
                            )}
                          </div>
                        </div>
                      </GlassmorphicCard>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
