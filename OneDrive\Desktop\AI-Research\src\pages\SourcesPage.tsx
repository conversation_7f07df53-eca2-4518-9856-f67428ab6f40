import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Database, 
  Search, 
  Filter, 
  Plus, 
  Star, 
  Shield, 
  ShieldCheck, 
  ShieldX,
  Link,
  Eye,
  Edit,
  Trash2,
  MoreVertical,
  Calendar,
  User,
  Globe,
  BookOpen,
  FileText,
  ExternalLink,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';
import GlassmorphicCard from '../components/GlassmorphicCard';
import PageTransition from '../components/PageTransition';

type Source = {
  id: number;
  title: string;
  url: string;
  domain: string;
  type: 'academic' | 'news' | 'government' | 'organization' | 'blog' | 'database';
  credibilityScore: number;
  credibilityLevel: 'high' | 'medium' | 'low' | 'unverified';
  dateAdded: string;
  lastVerified: string;
  author?: string;
  description: string;
  tags: string[];
  usageCount: number;
  isBookmarked: boolean;
  verificationStatus: 'verified' | 'pending' | 'failed';
  notes?: string;
};

type SourceFilter = 'all' | 'academic' | 'news' | 'government' | 'organization' | 'blog' | 'database';
type CredibilityFilter = 'all' | 'high' | 'medium' | 'low' | 'unverified';

export default function SourcesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState<SourceFilter>('all');
  const [credibilityFilter, setCredibilityFilter] = useState<CredibilityFilter>('all');
  const [selectedSources, setSelectedSources] = useState<number[]>([]);

  // Mock sources data
  const [sources] = useState<Source[]>([
    {
      id: 1,
      title: "Nature - International Journal of Science",
      url: "https://www.nature.com",
      domain: "nature.com",
      type: "academic",
      credibilityScore: 95,
      credibilityLevel: "high",
      dateAdded: "2024-01-15",
      lastVerified: "2024-01-15",
      author: "Nature Publishing Group",
      description: "Leading international journal publishing the finest peer-reviewed research in all fields of science and technology.",
      tags: ["Peer-reviewed", "Science", "Research", "Academic"],
      usageCount: 45,
      isBookmarked: true,
      verificationStatus: "verified",
      notes: "Highly reputable scientific journal with rigorous peer review process."
    },
    {
      id: 2,
      title: "MIT Technology Review",
      url: "https://www.technologyreview.com",
      domain: "technologyreview.com",
      type: "news",
      credibilityScore: 88,
      credibilityLevel: "high",
      dateAdded: "2024-01-14",
      lastVerified: "2024-01-14",
      author: "MIT",
      description: "Technology news and analysis from MIT's independent media company.",
      tags: ["Technology", "Innovation", "MIT", "Analysis"],
      usageCount: 32,
      isBookmarked: true,
      verificationStatus: "verified"
    },
    {
      id: 3,
      title: "NASA Climate Change and Global Warming",
      url: "https://climate.nasa.gov",
      domain: "nasa.gov",
      type: "government",
      credibilityScore: 97,
      credibilityLevel: "high",
      dateAdded: "2024-01-13",
      lastVerified: "2024-01-13",
      author: "NASA",
      description: "NASA's official climate change information and data portal.",
      tags: ["Climate", "NASA", "Government", "Data"],
      usageCount: 28,
      isBookmarked: false,
      verificationStatus: "verified"
    },
    {
      id: 4,
      title: "World Health Organization",
      url: "https://www.who.int",
      domain: "who.int",
      type: "organization",
      credibilityScore: 92,
      credibilityLevel: "high",
      dateAdded: "2024-01-12",
      lastVerified: "2024-01-12",
      author: "WHO",
      description: "The directing and coordinating authority for health within the United Nations system.",
      tags: ["Health", "WHO", "Global", "Medical"],
      usageCount: 19,
      isBookmarked: true,
      verificationStatus: "verified"
    },
    {
      id: 5,
      title: "TechCrunch",
      url: "https://techcrunch.com",
      domain: "techcrunch.com",
      type: "news",
      credibilityScore: 75,
      credibilityLevel: "medium",
      dateAdded: "2024-01-11",
      lastVerified: "2024-01-11",
      description: "Technology news and startup information.",
      tags: ["Technology", "Startups", "News", "Business"],
      usageCount: 15,
      isBookmarked: false,
      verificationStatus: "verified"
    },
    {
      id: 6,
      title: "Random Tech Blog",
      url: "https://randomtechblog.com",
      domain: "randomtechblog.com",
      type: "blog",
      credibilityScore: 45,
      credibilityLevel: "low",
      dateAdded: "2024-01-10",
      lastVerified: "2024-01-10",
      description: "Personal blog about technology trends.",
      tags: ["Technology", "Personal", "Opinion"],
      usageCount: 3,
      isBookmarked: false,
      verificationStatus: "pending"
    }
  ]);

  const filteredSources = sources.filter(source => {
    const matchesSearch = source.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         source.domain.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         source.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         source.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesType = typeFilter === 'all' || source.type === typeFilter;
    const matchesCredibility = credibilityFilter === 'all' || source.credibilityLevel === credibilityFilter;
    
    return matchesSearch && matchesType && matchesCredibility;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'academic':
        return <BookOpen size={16} className="text-blue-500" />;
      case 'news':
        return <FileText size={16} className="text-green-500" />;
      case 'government':
        return <Shield size={16} className="text-purple-500" />;
      case 'organization':
        return <Globe size={16} className="text-orange-500" />;
      case 'blog':
        return <User size={16} className="text-pink-500" />;
      case 'database':
        return <Database size={16} className="text-indigo-500" />;
      default:
        return <FileText size={16} className="text-gray-500" />;
    }
  };

  const getCredibilityIcon = (level: string) => {
    switch (level) {
      case 'high':
        return <ShieldCheck size={16} className="text-green-500" />;
      case 'medium':
        return <Shield size={16} className="text-yellow-500" />;
      case 'low':
        return <ShieldX size={16} className="text-red-500" />;
      default:
        return <AlertTriangle size={16} className="text-gray-500" />;
    }
  };

  const getVerificationIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle size={14} className="text-green-500" />;
      case 'pending':
        return <AlertTriangle size={14} className="text-yellow-500" />;
      case 'failed':
        return <XCircle size={14} className="text-red-500" />;
      default:
        return <AlertTriangle size={14} className="text-gray-500" />;
    }
  };

  const getCredibilityColor = (level: string) => {
    switch (level) {
      case 'high':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'academic':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'news':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'government':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      case 'organization':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'blog':
        return 'bg-pink-100 text-pink-800 dark:bg-pink-900/30 dark:text-pink-300';
      case 'database':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const renderStars = (score: number) => {
    const stars = Math.round(score / 20); // Convert to 5-star scale
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        size={12}
        className={i < stars ? 'text-yellow-400 fill-current' : 'text-gray-300'}
      />
    ));
  };

  const handleSelectSource = (sourceId: number) => {
    setSelectedSources(prev => 
      prev.includes(sourceId) 
        ? prev.filter(id => id !== sourceId)
        : [...prev, sourceId]
    );
  };

  return (
    <PageTransition>
      <div className="flex-1 overflow-hidden">
        <div className="h-full flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">Sources</h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  Manage and verify your research sources
                </p>
              </div>
              <div className="flex items-center gap-3">
                <motion.button
                  className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Plus size={18} className="mr-2 inline" />
                  Add Source
                </motion.button>
              </div>
            </div>

            {/* Search and Filters */}
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1 relative">
                <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search sources..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div className="flex gap-2">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value as SourceFilter)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Types</option>
                  <option value="academic">Academic</option>
                  <option value="news">News</option>
                  <option value="government">Government</option>
                  <option value="organization">Organization</option>
                  <option value="blog">Blog</option>
                  <option value="database">Database</option>
                </select>
                
                <select
                  value={credibilityFilter}
                  onChange={(e) => setCredibilityFilter(e.target.value as CredibilityFilter)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All Credibility</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                  <option value="unverified">Unverified</option>
                </select>
              </div>
            </div>

            {/* Bulk Actions */}
            {selectedSources.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-4 p-3 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg flex items-center justify-between"
              >
                <span className="text-sm text-indigo-700 dark:text-indigo-300">
                  {selectedSources.length} source(s) selected
                </span>
                <div className="flex gap-2">
                  <button className="px-3 py-1 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700">
                    Verify
                  </button>
                  <button className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700">
                    <Trash2 size={14} className="mr-1 inline" />
                    Delete
                  </button>
                </div>
              </motion.div>
            )}
          </div>

          {/* Sources Grid */}
          <div className="flex-1 overflow-y-auto p-6">
            {filteredSources.length === 0 ? (
              <div className="text-center py-12">
                <Database size={48} className="mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No sources found</h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {searchTerm ? 'Try adjusting your search terms' : 'Add your first research source to get started'}
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredSources.map((source, index) => (
                  <GlassmorphicCard key={source.id} delay={index}>
                    <div className="p-5">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            checked={selectedSources.includes(source.id)}
                            onChange={() => handleSelectSource(source.id)}
                            className="rounded border-gray-300"
                          />
                          {getTypeIcon(source.type)}
                          <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(source.type)}`}>
                            {source.type}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          {getVerificationIcon(source.verificationStatus)}
                          <button className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-800">
                            <MoreVertical size={16} className="text-gray-400" />
                          </button>
                        </div>
                      </div>

                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2">
                        {source.title}
                      </h3>
                      
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {source.domain}
                      </p>

                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                        {source.description}
                      </p>

                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-2">
                          {getCredibilityIcon(source.credibilityLevel)}
                          <span className={`px-2 py-1 text-xs rounded-full ${getCredibilityColor(source.credibilityLevel)}`}>
                            {source.credibilityLevel}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          {renderStars(source.credibilityScore)}
                          <span className="text-xs text-gray-500 ml-1">({source.credibilityScore})</span>
                        </div>
                      </div>

                      <div className="flex flex-wrap gap-1 mb-3">
                        {source.tags.slice(0, 3).map(tag => (
                          <span key={tag} className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded">
                            {tag}
                          </span>
                        ))}
                        {source.tags.length > 3 && (
                          <span className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 rounded">
                            +{source.tags.length - 3}
                          </span>
                        )}
                      </div>

                      <div className="text-xs text-gray-500 dark:text-gray-400 mb-4 space-y-1">
                        <div className="flex justify-between">
                          <span>Used: {source.usageCount} times</span>
                          <span>Added: {new Date(source.dateAdded).toLocaleDateString()}</span>
                        </div>
                        <div>
                          <span>Last verified: {new Date(source.lastVerified).toLocaleDateString()}</span>
                        </div>
                      </div>

                      <div className="flex gap-2">
                        <button className="flex-1 px-3 py-2 text-sm bg-indigo-600 text-white rounded hover:bg-indigo-700 transition-colors">
                          <Eye size={14} className="mr-1 inline" />
                          View
                        </button>
                        <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <ExternalLink size={14} />
                        </button>
                        <button className="px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                          <Edit size={14} />
                        </button>
                      </div>
                    </div>
                  </GlassmorphicCard>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </PageTransition>
  );
}
