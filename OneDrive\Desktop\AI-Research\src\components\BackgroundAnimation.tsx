import { useEffect, useRef } from 'react';

type Particle = {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
  opacity: number;
  pulse: number;
}

type GeometricShape = {
  x: number;
  y: number;
  size: number;
  rotation: number;
  rotationSpeed: number;
  speedX: number;
  speedY: number;
  type: 'triangle' | 'square' | 'hexagon' | 'circle';
  color: string;
  opacity: number;
}

type EnergyWave = {
  x: number;
  y: number;
  radius: number;
  maxRadius: number;
  speed: number;
  opacity: number;
  color: string;
}

const BackgroundAnimation = () => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas to full screen
    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    window.addEventListener('resize', handleResize);
    handleResize();

    // Enhanced particles with pulsing effect
    const particles: Particle[] = [];
    const particleCount = Math.min(Math.floor(window.innerWidth * 0.05), 120);
    const particleColors = [
      'rgba(99, 102, 241, 0.8)',   // Indigo
      'rgba(168, 85, 247, 0.7)',   // Purple
      'rgba(59, 130, 246, 0.6)',   // Blue
      'rgba(16, 185, 129, 0.5)',   // Emerald
      'rgba(245, 101, 101, 0.4)',  // Red
      'rgba(251, 191, 36, 0.6)'    // Amber
    ];

    for (let i = 0; i < particleCount; i++) {
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size: Math.random() * 3 + 1,
        speedX: (Math.random() - 0.5) * 0.3,
        speedY: (Math.random() - 0.5) * 0.3,
        color: particleColors[Math.floor(Math.random() * particleColors.length)],
        opacity: Math.random() * 0.5 + 0.3,
        pulse: Math.random() * Math.PI * 2
      });
    }

    // Geometric shapes for futuristic effect
    const shapes: GeometricShape[] = [];
    const shapeCount = Math.min(Math.floor(window.innerWidth * 0.008), 15);
    const shapeColors = [
      'rgba(99, 102, 241, 0.1)',
      'rgba(168, 85, 247, 0.08)',
      'rgba(59, 130, 246, 0.06)',
      'rgba(16, 185, 129, 0.05)'
    ];

    for (let i = 0; i < shapeCount; i++) {
      shapes.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size: Math.random() * 60 + 20,
        rotation: Math.random() * Math.PI * 2,
        rotationSpeed: (Math.random() - 0.5) * 0.02,
        speedX: (Math.random() - 0.5) * 0.1,
        speedY: (Math.random() - 0.5) * 0.1,
        type: ['triangle', 'square', 'hexagon', 'circle'][Math.floor(Math.random() * 4)] as GeometricShape['type'],
        color: shapeColors[Math.floor(Math.random() * shapeColors.length)],
        opacity: Math.random() * 0.3 + 0.1
      });
    }

    // Energy waves
    const waves: EnergyWave[] = [];

    // Create energy wave periodically
    const createWave = () => {
      waves.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: 0,
        maxRadius: Math.random() * 200 + 100,
        speed: Math.random() * 2 + 1,
        opacity: 0.3,
        color: particleColors[Math.floor(Math.random() * particleColors.length)]
      });
    };

    // Connect particles with enhanced lines
    const connectParticles = () => {
      for (let a = 0; a < particles.length; a++) {
        for (let b = a; b < particles.length; b++) {
          const dx = particles[a].x - particles[b].x;
          const dy = particles[a].y - particles[b].y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance < 150) {
            const opacity = 0.2 * (1 - distance / 150);
            ctx.beginPath();
            ctx.strokeStyle = `rgba(99, 102, 241, ${opacity})`;
            ctx.lineWidth = 1;
            ctx.moveTo(particles[a].x, particles[a].y);
            ctx.lineTo(particles[b].x, particles[b].y);
            ctx.stroke();

            // Add glow effect
            ctx.shadowColor = 'rgba(99, 102, 241, 0.5)';
            ctx.shadowBlur = 2;
            ctx.stroke();
            ctx.shadowBlur = 0;
          }
        }
      }
    };

    // Draw geometric shapes
    const drawShape = (shape: GeometricShape) => {
      ctx.save();
      ctx.translate(shape.x, shape.y);
      ctx.rotate(shape.rotation);
      ctx.globalAlpha = shape.opacity;
      ctx.strokeStyle = shape.color;
      ctx.lineWidth = 2;

      switch (shape.type) {
        case 'triangle':
          ctx.beginPath();
          ctx.moveTo(0, -shape.size / 2);
          ctx.lineTo(-shape.size / 2, shape.size / 2);
          ctx.lineTo(shape.size / 2, shape.size / 2);
          ctx.closePath();
          ctx.stroke();
          break;
        case 'square':
          ctx.strokeRect(-shape.size / 2, -shape.size / 2, shape.size, shape.size);
          break;
        case 'hexagon':
          ctx.beginPath();
          for (let i = 0; i < 6; i++) {
            const angle = (i * Math.PI) / 3;
            const x = Math.cos(angle) * shape.size / 2;
            const y = Math.sin(angle) * shape.size / 2;
            if (i === 0) ctx.moveTo(x, y);
            else ctx.lineTo(x, y);
          }
          ctx.closePath();
          ctx.stroke();
          break;
        case 'circle':
          ctx.beginPath();
          ctx.arc(0, 0, shape.size / 2, 0, Math.PI * 2);
          ctx.stroke();
          break;
      }
      ctx.restore();
    };

    // Animation loop with enhanced effects
    let time = 0;
    const animate = () => {
      requestAnimationFrame(animate);
      time += 0.016; // ~60fps

      // Create gradient background
      const gradient = ctx.createRadialGradient(
        canvas.width / 2, canvas.height / 2, 0,
        canvas.width / 2, canvas.height / 2, Math.max(canvas.width, canvas.height)
      );
      gradient.addColorStop(0, 'rgba(15, 23, 42, 0.95)');
      gradient.addColorStop(0.5, 'rgba(30, 41, 59, 0.9)');
      gradient.addColorStop(1, 'rgba(51, 65, 85, 0.85)');

      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Create energy wave periodically
      if (Math.random() < 0.003) {
        createWave();
      }

      // Update and draw energy waves
      waves.forEach((wave, index) => {
        wave.radius += wave.speed;
        wave.opacity *= 0.995;

        if (wave.radius > wave.maxRadius || wave.opacity < 0.01) {
          waves.splice(index, 1);
          return;
        }

        ctx.beginPath();
        ctx.arc(wave.x, wave.y, wave.radius, 0, Math.PI * 2);
        ctx.strokeStyle = wave.color.replace(/[\d\.]+\)$/g, `${wave.opacity})`);
        ctx.lineWidth = 2;
        ctx.stroke();
      });

      // Update and draw particles with pulsing effect
      particles.forEach(particle => {
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        particle.pulse += 0.05;

        // Bounce off edges with some randomness
        if (particle.x > canvas.width || particle.x < 0) {
          particle.speedX = -particle.speedX * (0.8 + Math.random() * 0.4);
        }

        if (particle.y > canvas.height || particle.y < 0) {
          particle.speedY = -particle.speedY * (0.8 + Math.random() * 0.4);
        }

        // Pulsing size effect
        const pulseFactor = 1 + Math.sin(particle.pulse) * 0.3;
        const currentSize = particle.size * pulseFactor;

        // Draw particle with glow
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, currentSize, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();

        // Add glow effect
        ctx.shadowColor = particle.color;
        ctx.shadowBlur = 10;
        ctx.fill();
        ctx.shadowBlur = 0;
      });

      // Update and draw geometric shapes
      shapes.forEach(shape => {
        shape.x += shape.speedX;
        shape.y += shape.speedY;
        shape.rotation += shape.rotationSpeed;

        // Wrap around edges
        if (shape.x > canvas.width + shape.size) shape.x = -shape.size;
        if (shape.x < -shape.size) shape.x = canvas.width + shape.size;
        if (shape.y > canvas.height + shape.size) shape.y = -shape.size;
        if (shape.y < -shape.size) shape.y = canvas.height + shape.size;

        drawShape(shape);
      });

      connectParticles();

      // Add scanning lines effect
      ctx.globalAlpha = 0.1;
      ctx.strokeStyle = 'rgba(99, 102, 241, 0.3)';
      ctx.lineWidth = 1;
      for (let i = 0; i < 3; i++) {
        const y = (time * 50 + i * canvas.height / 3) % canvas.height;
        ctx.beginPath();
        ctx.moveTo(0, y);
        ctx.lineTo(canvas.width, y);
        ctx.stroke();
      }
      ctx.globalAlpha = 1;
    };

    animate();
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  return (
    <canvas 
      ref={canvasRef} 
      className="fixed inset-0 w-full h-full -z-10 opacity-60"
    />
  );
};

export default BackgroundAnimation;
