import { <PERSON>R<PERSON>, BookOpen, Check, Clock, Database, EllipsisVertical, FileText, Loader, Pause, Play, Shield, Trash2 } from 'lucide-react';
import { motion } from 'framer-motion';
import { useState } from 'react';
import { Research, useResearch } from '../context/ResearchContext';
import ReportPreviewModal from './ReportPreviewModal';
import SourceCredibilityModal from './SourceCredibilityModal';
import GlassmorphicCard from './GlassmorphicCard';

type ResearchCardProps = {
  research: Research;
  index: number;
};

export default function ResearchCard({ research, index }: ResearchCardProps) {
  const { completeResearch, pauseResearch, resumeResearch, deleteResearch } = useResearch();
  const [showMenu, setShowMenu] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [showSourceModal, setShowSourceModal] = useState(false);
  
  const isInProgress = research.status === 'in-progress';
  
  const handleMoreClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowMenu(!showMenu);
  };
  
  const handlePauseResume = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (isInProgress) {
      pauseResearch(research.id);
    } else if (research.status === 'paused') {
      resumeResearch(research.id);
    }
    setShowMenu(false);
  };
  
  const handleComplete = (e: React.MouseEvent) => {
    e.stopPropagation();
    completeResearch(research.id);
    setShowMenu(false);
  };
  
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this research?')) {
      deleteResearch(research.id);
    }
    setShowMenu(false);
  };
  
  const handleViewReport = () => {
    setShowReportModal(true);
  };
  
  return (
    <>
      <GlassmorphicCard delay={index} className="overflow-hidden">
        <div className="p-5">
          <div className="flex justify-between items-start">
            <h3 className="font-semibold text-gray-800 dark:text-gray-100 text-lg">{research.title}</h3>
            <div className="flex items-center gap-2">
              {isInProgress ? (
                <motion.div 
                  className="flex items-center gap-1.5 px-2.5 py-1 bg-indigo-100 dark:bg-indigo-900/30 text-indigo-700 dark:text-indigo-300 rounded-full text-xs font-medium"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                >
                  <Loader size={12} className="animate-spin" />
                  <span>In Progress</span>
                </motion.div>
              ) : research.status === 'paused' ? (
                <div className="flex items-center gap-1.5 px-2.5 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-700 dark:text-yellow-300 rounded-full text-xs font-medium">
                  <Clock size={12} />
                  <span>Paused</span>
                </div>
              ) : (
                <div className="flex items-center gap-1.5 px-2.5 py-1 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs font-medium">
                  <Check size={12} />
                  <span>Completed</span>
                </div>
              )}
              <button 
                className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                onClick={handleMoreClick}
              >
                <EllipsisVertical size={16} className="text-gray-500 dark:text-gray-400" />
              </button>
              
              {showMenu && (
                <motion.div 
                  className="absolute top-12 right-4 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-100 dark:border-gray-700 py-1 z-10"
                  initial={{ opacity: 0, y: 10, scale: 0.95 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{ duration: 0.1 }}
                >
                  {(isInProgress || research.status === 'paused') && (
                    <button 
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
                      onClick={handlePauseResume}
                    >
                      {isInProgress ? <Pause size={14} /> : <Play size={14} />}
                      {isInProgress ? 'Pause Research' : 'Resume Research'}
                    </button>
                  )}
                  {isInProgress && (
                    <button 
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center gap-2"
                      onClick={handleComplete}
                    >
                      <Check size={14} />
                      Complete Research
                    </button>
                  )}
                  <button 
                    className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 flex items-center gap-2"
                    onClick={handleDelete}
                  >
                    <Trash2 size={14} />
                    Delete Research
                  </button>
                </motion.div>
              )}
            </div>
          </div>
          
          <p className="text-gray-600 dark:text-gray-400 text-sm mt-2">{research.description}</p>
          
          {(isInProgress || research.status === 'paused') && (
            <div className="mt-4">
              <div className="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1.5">
                <span>Research Progress</span>
                <span>{research.progress}%</span>
              </div>
              <div className="w-full h-2 bg-gray-100 dark:bg-gray-800 rounded-full overflow-hidden">
                <motion.div 
                  className={`h-full ${
                    research.status === 'paused' 
                      ? 'bg-yellow-400 dark:bg-yellow-500' 
                      : 'bg-gradient-to-r from-indigo-500 to-purple-500'
                  }`}
                  initial={{ width: 0 }}
                  animate={{ width: `${research.progress}%` }}
                  transition={{ duration: 1 }}
                />
              </div>
            </div>
          )}
          
          <div className="grid grid-cols-2 gap-3 mt-4">
            <div className="flex items-center gap-2">
              <BookOpen size={16} className="text-gray-500 dark:text-gray-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400">
                <span className="font-medium text-gray-800 dark:text-gray-200">{research.sourcesScanned}</span> Sources
              </span>
            </div>
            
            <div 
              className="flex items-center gap-2 cursor-pointer hover:text-indigo-600 dark:hover:text-indigo-400 group"
              onClick={(e) => {
                e.stopPropagation();
                setShowSourceModal(true);
              }}
            >
              <Shield size={16} className="text-gray-500 dark:text-gray-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400" />
              <span className="text-sm text-gray-600 dark:text-gray-400 group-hover:text-indigo-600 dark:group-hover:text-indigo-400">
                <span className="font-medium text-gray-800 dark:text-gray-200 group-hover:text-indigo-600 dark:group-hover:text-indigo-400">{research.credibleSourcesFound}</span> Verified
              </span>
            </div>
            
            {isInProgress || research.status === 'paused' ? (
              <div className="flex items-center gap-2">
                <Clock size={16} className="text-gray-500 dark:text-gray-400" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  <span className="font-medium text-gray-800 dark:text-gray-200">{research.timeElapsed}</span> Elapsed
                </span>
              </div>
            ) : (
              <div className="flex items-center gap-2 col-span-2">
                <Clock size={16} className="text-gray-500 dark:text-gray-400" />
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Completed on <span className="font-medium text-gray-800 dark:text-gray-200">{research.completedDate}</span>
                </span>
              </div>
            )}
          </div>
        </div>
        
        <div className="border-t border-gray-100 dark:border-gray-800 p-3 bg-gray-50 dark:bg-gray-800/50 flex justify-between items-center">
          <motion.button 
            className="flex items-center gap-1.5 text-sm font-medium text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 transition-colors"
            onClick={handleViewReport}
            whileHover={{ x: 5 }}
            transition={{ type: "spring", stiffness: 400, damping: 10 }}
          >
            <FileText size={16} />
            <span>{isInProgress ? 'Preview Report' : 'View Report'}</span>
          </motion.button>
          
          <motion.button 
            className="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
            onClick={handleViewReport}
            whileHover={{ x: 3 }}
            whileTap={{ scale: 0.9 }}
          >
            <ArrowRight size={16} className="text-gray-600 dark:text-gray-300" />
          </motion.button>
        </div>
      </GlassmorphicCard>
      
      <ReportPreviewModal 
        isOpen={showReportModal} 
        onClose={() => setShowReportModal(false)} 
        research={research} 
      />
      
      <SourceCredibilityModal
        isOpen={showSourceModal}
        onClose={() => setShowSourceModal(false)}
        research={research}
      />
    </>
  );
}
