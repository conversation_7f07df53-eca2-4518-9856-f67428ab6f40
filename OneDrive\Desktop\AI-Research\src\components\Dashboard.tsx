import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Brain, Check, CirclePlus, Clock, Database, FileText, Shield, TrendingUp } from 'lucide-react';
import ResearchCard from './ResearchCard';
import ProgressChart from './ProgressChart';
import ActivityFeed from './ActivityFeed';
import { useResearch } from '../context/ResearchContext';
import { useState, useEffect } from 'react';

export default function Dashboard({ onNewResearch }: { onNewResearch?: () => void }) {
  const { getInProgressResearches, getCompletedResearches, simulateProgress } = useResearch();
  const [inProgressResearch, setInProgressResearch] = useState(getInProgressResearches());
  const [completedResearch, setCompletedResearch] = useState(getCompletedResearches());
  const [stats, setStats] = useState([
    { icon: <FileText className="text-purple-500" />, label: "Reports Generated", value: "24" },
    { icon: <BookOpen className="text-green-500" />, label: "Sources Analyzed", value: "836" },
    { icon: <Shield className="text-blue-500" />, label: "Sources Verified", value: "412" },
    { icon: <Brain className="text-indigo-500" />, label: "AI Research Hours", value: "62.5" }
  ]);

  // Simulate research progress
  useEffect(() => {
    const intervalId = setInterval(() => {
      const inProgress = getInProgressResearches();
      inProgress.forEach(research => {
        simulateProgress(research.id);
      });
      
      // Update research lists
      setInProgressResearch(getInProgressResearches());
      setCompletedResearch(getCompletedResearches());
      
      // Update stats
      const totalSourcesScanned = [...inProgress, ...getCompletedResearches()]
        .reduce((sum, research) => sum + research.sourcesScanned, 0);
      
      const totalCredibleSources = [...inProgress, ...getCompletedResearches()]
        .reduce((sum, research) => sum + research.credibleSourcesFound, 0);
      
      const totalReports = getCompletedResearches().length;
      
      // Calculate research hours based on completed reports
      const researchHours = (totalSourcesScanned * 0.075).toFixed(1);
      
      setStats([
        { icon: <FileText className="text-purple-500" />, label: "Reports Generated", value: String(totalReports) },
        { icon: <BookOpen className="text-green-500" />, label: "Sources Analyzed", value: String(totalSourcesScanned) },
        { icon: <Shield className="text-blue-500" />, label: "Sources Verified", value: String(totalCredibleSources) },
        { icon: <Brain className="text-indigo-500" />, label: "AI Research Hours", value: researchHours }
      ]);
    }, 5000); // Update every 5 seconds
    
    return () => clearInterval(intervalId);
  }, [getInProgressResearches, getCompletedResearches, simulateProgress]);

  return (
    <div className="flex-1 p-6 overflow-y-auto bg-gray-50">
      <div className="flex flex-col gap-8">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Research Dashboard</h1>
          <p className="text-gray-600 mt-1">Manage your automated research projects</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white p-5 rounded-xl shadow-sm border border-gray-200">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-gray-100 rounded-lg">
                  {stat.icon}
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-800">{stat.value}</h3>
                  <p className="text-gray-500 text-sm">{stat.label}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-200">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-lg font-semibold text-gray-800">Research Progress</h2>
                <select className="px-3 py-1.5 bg-gray-100 rounded-md text-sm border-none outline-none">
                  <option value="7days">Last 7 days</option>
                  <option value="30days">Last 30 days</option>
                  <option value="90days">Last 90 days</option>
                </select>
              </div>
              <ProgressChart />
            </div>
          </div>
          
          <div>
            <div className="bg-white p-5 rounded-xl shadow-sm border border-gray-200 h-full">
              <div className="flex justify-between items-center mb-5">
                <h2 className="text-lg font-semibold text-gray-800">Recent Activity</h2>
              </div>
              <ActivityFeed 
                inProgressResearch={inProgressResearch}
                completedResearch={completedResearch}
              />
            </div>
          </div>
        </div>
        
        <div>
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-lg font-semibold text-gray-800">In Progress</h2>
            <button
              className="flex items-center gap-1 text-indigo-600 font-medium text-sm"
              onClick={() => onNewResearch?.()}
            >
              <CirclePlus size={16} />
              <span>New Research</span>
            </button>
          </div>
          {inProgressResearch.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {inProgressResearch.map(research => (
                <ResearchCard key={research.id} research={research} />
              ))}
            </div>
          ) : (
            <div className="bg-white p-8 rounded-xl border border-gray-200 text-center">
              <p className="text-gray-500">No research in progress. Start a new research project!</p>
              <button
                className="mt-4 px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                onClick={() => onNewResearch?.()}
              >
                Start New Research
              </button>
            </div>
          )}
        </div>
        
        <div>
          <h2 className="text-lg font-semibold text-gray-800 mb-4">Completed Research</h2>
          {completedResearch.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {completedResearch.map(research => (
                <ResearchCard key={research.id} research={research} />
              ))}
            </div>
          ) : (
            <div className="bg-white p-8 rounded-xl border border-gray-200 text-center">
              <p className="text-gray-500">No completed research yet. Start a new research project!</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
