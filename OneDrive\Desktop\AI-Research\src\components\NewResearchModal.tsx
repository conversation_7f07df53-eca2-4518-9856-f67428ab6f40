import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Brain, ChevronDown, Clock, Database, FileText, GraduationCap, LayoutGrid, Newspaper, PenLine, Shield, X } from 'lucide-react';
import { Research, useResearch } from '../context/ResearchContext';

type NewResearchModalProps = {
  isOpen: boolean;
  onClose: () => void;
};

export default function NewResearchModal({ isOpen, onClose }: NewResearchModalProps) {
  const { addResearch } = useResearch();
  
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [sources, setSources] = useState<string[]>(['Academic', 'News']);
  const [timeRange, setTimeRange] = useState('5');
  const [researchDepth, setResearchDepth] = useState('2');
  const [selectedAIModel, setSelectedAIModel] = useState('GPT-4');
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  useEffect(() => {
    if (isOpen) {
      // Reset form when modal opens
      setTitle('');
      setDescription('');
      setSources(['Academic', 'News']);
      setTimeRange('5');
      setResearchDepth('2');
      setSelectedAIModel('GPT-4');
      setCurrentStep(1);
      setIsSubmitting(false);
    }
  }, [isOpen]);
  
  const handleSourceToggle = (source: string) => {
    if (sources.includes(source)) {
      // Don't allow removing if it would result in empty selection
      if (sources.length > 1) {
        setSources(sources.filter(s => s !== source));
      }
    } else {
      setSources([...sources, source]);
    }
  };
  
  const handleSubmit = () => {
    setIsSubmitting(true);
    
    // Prepare new research object
    const newResearch: Omit<Research, 'id' | 'createdAt'> = {
      title,
      description,
      progress: 0,
      sourcesScanned: 5, // Start with a few sources already scanned
      credibleSourcesFound: 3, // Start with some credible sources
      timeElapsed: '0h 5m',
      status: 'in-progress',
      sources,
      researchDepth,
      timeRange,
      aiModel: selectedAIModel
    };
    
    // Simulate a short delay for "processing"
    setTimeout(() => {
      addResearch(newResearch);
      setIsSubmitting(false);
      onClose();
    }, 1500);
  };
  
  const nextStep = () => {
    setCurrentStep(currentStep + 1);
  };
  
  const prevStep = () => {
    setCurrentStep(currentStep - 1);
  };
  
  if (!isOpen) return null;
  
  return (
    <motion.div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={(e) => {
        if (e.target === e.currentTarget) onClose();
      }}
    >
      <motion.div
        className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] flex flex-col"
        initial={{ scale: 0.9, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        exit={{ scale: 0.9, y: 20 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-indigo-100 rounded-lg text-indigo-600">
              <Brain size={22} />
            </div>
            <div>
              <h2 className="text-xl font-bold text-gray-800">New Research Project</h2>
              <p className="text-sm text-gray-500">Set up your AI-powered research</p>
            </div>
          </div>
          <button 
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <X size={20} className="text-gray-500" />
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-6">
          {/* Step 1: Research Topic */}
          {currentStep === 1 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-lg font-semibold text-gray-800 mb-1">Research Topic</h3>
              <p className="text-gray-600 text-sm mb-6">Define what you want the AI to research for you</p>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">Research Title</label>
                  <input 
                    type="text" 
                    id="title"
                    placeholder="e.g., Artificial Intelligence in Healthcare"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                  />
                  <p className="text-gray-500 text-xs mt-1">Be specific about the topic you want researched</p>
                </div>
                
                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">Additional Context (Optional)</label>
                  <textarea 
                    id="description"
                    placeholder="e.g., Focus on recent breakthroughs and ethical considerations"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                    className="w-full px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                  />
                  <p className="text-gray-500 text-xs mt-1">Add any specific aspects or angles you want covered</p>
                </div>
              </div>
            </motion.div>
          )}
          
          {/* Step 2: Research Sources */}
          {currentStep === 2 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-lg font-semibold text-gray-800 mb-1">Research Sources</h3>
              <p className="text-gray-600 text-sm mb-6">Select which types of sources the AI should analyze</p>
              
              <div className="grid grid-cols-2 gap-3 mb-6">
                <button 
                  className={`p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                    sources.includes('Academic') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleSourceToggle('Academic')}
                >
                  <div className={`p-2 rounded-lg ${
                    sources.includes('Academic') ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <GraduationCap size={20} />
                  </div>
                  <div>
                    <h4 className={`font-medium ${
                      sources.includes('Academic') ? 'text-indigo-700' : 'text-gray-700'
                    }`}>Academic</h4>
                    <p className="text-xs text-gray-500 mt-1">Scholarly articles, journals, research papers</p>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                    sources.includes('Scientific') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleSourceToggle('Scientific')}
                >
                  <div className={`p-2 rounded-lg ${
                    sources.includes('Scientific') ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <Brain size={20} />
                  </div>
                  <div>
                    <h4 className={`font-medium ${
                      sources.includes('Scientific') ? 'text-indigo-700' : 'text-gray-700'
                    }`}>Scientific</h4>
                    <p className="text-xs text-gray-500 mt-1">Lab studies, experimental data, methodology</p>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                    sources.includes('News') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleSourceToggle('News')}
                >
                  <div className={`p-2 rounded-lg ${
                    sources.includes('News') ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <Newspaper size={20} />
                  </div>
                  <div>
                    <h4 className={`font-medium ${
                      sources.includes('News') ? 'text-indigo-700' : 'text-gray-700'
                    }`}>News</h4>
                    <p className="text-xs text-gray-500 mt-1">Recent events, current reporting, media analysis</p>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                    sources.includes('Industry') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleSourceToggle('Industry')}
                >
                  <div className={`p-2 rounded-lg ${
                    sources.includes('Industry') ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <LayoutGrid size={20} />
                  </div>
                  <div>
                    <h4 className={`font-medium ${
                      sources.includes('Industry') ? 'text-indigo-700' : 'text-gray-700'
                    }`}>Industry</h4>
                    <p className="text-xs text-gray-500 mt-1">Market reports, business analysis, white papers</p>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                    sources.includes('Government') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleSourceToggle('Government')}
                >
                  <div className={`p-2 rounded-lg ${
                    sources.includes('Government') ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <Shield size={20} />
                  </div>
                  <div>
                    <h4 className={`font-medium ${
                      sources.includes('Government') ? 'text-indigo-700' : 'text-gray-700'
                    }`}>Government</h4>
                    <p className="text-xs text-gray-500 mt-1">Official reports, regulations, public data</p>
                  </div>
                </button>
                
                <button 
                  className={`p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                    sources.includes('Web') ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                  }`}
                  onClick={() => handleSourceToggle('Web')}
                >
                  <div className={`p-2 rounded-lg ${
                    sources.includes('Web') ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                  }`}>
                    <PenLine size={20} />
                  </div>
                  <div>
                    <h4 className={`font-medium ${
                      sources.includes('Web') ? 'text-indigo-700' : 'text-gray-700'
                    }`}>Web</h4>
                    <p className="text-xs text-gray-500 mt-1">Blogs, forums, websites, digital content</p>
                  </div>
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor="timeRange" className="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
                  <div className="relative">
                    <select
                      id="timeRange"
                      value={timeRange}
                      onChange={(e) => setTimeRange(e.target.value)}
                      className="w-full appearance-none px-4 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors"
                    >
                      <option value="1">Last 1 year</option>
                      <option value="3">Last 3 years</option>
                      <option value="5">Last 5 years</option>
                      <option value="10">Last 10 years</option>
                      <option value="all">All time</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                      <ChevronDown size={18} className="text-gray-500" />
                    </div>
                  </div>
                  <p className="text-gray-500 text-xs mt-1">How far back should the AI search for information</p>
                </div>
              </div>
            </motion.div>
          )}
          
          {/* Step 3: Research Depth */}
          {currentStep === 3 && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
            >
              <h3 className="text-lg font-semibold text-gray-800 mb-1">Research Configuration</h3>
              <p className="text-gray-600 text-sm mb-6">Configure depth and AI model settings</p>
              
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Research Depth</label>
                <div className="space-y-3">
                  <button 
                    className={`w-full p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                      researchDepth === '1' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => setResearchDepth('1')}
                  >
                    <div className={`p-2 rounded-lg ${
                      researchDepth === '1' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                    }`}>
                      <Clock size={20} />
                    </div>
                    <div>
                      <h4 className={`font-medium ${
                        researchDepth === '1' ? 'text-indigo-700' : 'text-gray-700'
                      }`}>Basic (Faster)</h4>
                      <p className="text-xs text-gray-500 mt-1">Quick overview with key insights. ~100 sources analyzed.</p>
                    </div>
                  </button>
                  
                  <button 
                    className={`w-full p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                      researchDepth === '2' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => setResearchDepth('2')}
                  >
                    <div className={`p-2 rounded-lg ${
                      researchDepth === '2' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                    }`}>
                      <Database size={20} />
                    </div>
                    <div>
                      <h4 className={`font-medium ${
                        researchDepth === '2' ? 'text-indigo-700' : 'text-gray-700'
                      }`}>Standard (Recommended)</h4>
                      <p className="text-xs text-gray-500 mt-1">Comprehensive research with detailed analysis. ~250 sources analyzed.</p>
                    </div>
                  </button>
                  
                  <button 
                    className={`w-full p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                      researchDepth === '3' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => setResearchDepth('3')}
                  >
                    <div className={`p-2 rounded-lg ${
                      researchDepth === '3' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                    }`}>
                      <FileText size={20} />
                    </div>
                    <div>
                      <h4 className={`font-medium ${
                        researchDepth === '3' ? 'text-indigo-700' : 'text-gray-700'
                      }`}>Thorough (Slower)</h4>
                      <p className="text-xs text-gray-500 mt-1">Exhaustive deep dive with maximum source validation. ~500 sources analyzed.</p>
                    </div>
                  </button>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">AI Model</label>
                <div className="space-y-3">
                  <button 
                    className={`w-full p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                      selectedAIModel === 'GPT-4' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedAIModel('GPT-4')}
                  >
                    <div className={`p-2 rounded-lg ${
                      selectedAIModel === 'GPT-4' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                    }`}>
                      <Brain size={20} />
                    </div>
                    <div>
                      <h4 className={`font-medium ${
                        selectedAIModel === 'GPT-4' ? 'text-indigo-700' : 'text-gray-700'
                      }`}>GPT-4</h4>
                      <p className="text-xs text-gray-500 mt-1">Advanced reasoning with deeper contextual understanding</p>
                    </div>
                  </button>
                  
                  <button 
                    className={`w-full p-4 border rounded-lg text-left flex items-start gap-3 transition-colors ${
                      selectedAIModel === 'Claude 3' ? 'border-indigo-500 bg-indigo-50' : 'border-gray-200 hover:bg-gray-50'
                    }`}
                    onClick={() => setSelectedAIModel('Claude 3')}
                  >
                    <div className={`p-2 rounded-lg ${
                      selectedAIModel === 'Claude 3' ? 'bg-indigo-100 text-indigo-600' : 'bg-gray-100 text-gray-500'
                    }`}>
                      <Brain size={20} />
                    </div>
                    <div>
                      <h4 className={`font-medium ${
                        selectedAIModel === 'Claude 3' ? 'text-indigo-700' : 'text-gray-700'
                      }`}>Claude 3</h4>
                      <p className="text-xs text-gray-500 mt-1">Strong reasoning with focus on evidence-based analysis</p>
                    </div>
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </div>
        
        <div className="border-t border-gray-200 p-4 bg-gray-50 rounded-b-xl flex justify-between items-center">
          {currentStep > 1 ? (
            <button
              onClick={prevStep}
              className="px-4 py-2 text-gray-700 font-medium rounded-lg hover:bg-gray-200 transition-colors"
            >
              Back
            </button>
          ) : (
            <div></div> // Empty div for flex spacing
          )}
          
          {currentStep < 3 ? (
            <button
              onClick={nextStep}
              className="px-4 py-2 text-white font-medium rounded-lg bg-indigo-600 hover:bg-indigo-700 transition-colors disabled:opacity-50"
              disabled={currentStep === 1 && !title.trim()}
            >
              Next
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              className="px-4 py-2 text-white font-medium rounded-lg bg-indigo-600 hover:bg-indigo-700 transition-colors flex items-center gap-2"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
                  <span>Processing...</span>
                </>
              ) : (
                <span>Start Research</span>
              )}
            </button>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
}
