import { AreaChart, Area, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Responsive<PERSON><PERSON><PERSON>, Legend } from 'recharts';

const data = [
  { day: 'Mon', sources: 24, verified: 16, reports: 2 },
  { day: 'Tue', sources: 42, verified: 28, reports: 3 },
  { day: 'Wed', sources: 38, verified: 23, reports: 2 },
  { day: 'Thu', sources: 50, verified: 32, reports: 4 },
  { day: 'Fri', sources: 65, verified: 41, reports: 5 },
  { day: 'Sat', sources: 37, verified: 22, reports: 2 },
  { day: 'Sun', sources: 28, verified: 19, reports: 2 },
];

export default function ProgressChart() {
  return (
    <div className="h-72">
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart
          data={data}
          margin={{ top: 10, right: 30, left: 0, bottom: 0 }}
        >
          <defs>
            <linearGradient id="colorSources" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#6366F1" stopOpacity={0.3} />
              <stop offset="95%" stopColor="#6366F1" stopOpacity={0} />
            </linearGradient>
            <linearGradient id="colorVerified" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.3} />
              <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0} />
            </linearGradient>
            <linearGradient id="colorReports" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#EC4899" stopOpacity={0.3} />
              <stop offset="95%" stopColor="#EC4899" stopOpacity={0} />
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
          <XAxis dataKey="day" axisLine={false} tickLine={false} />
          <YAxis axisLine={false} tickLine={false} />
          <Tooltip 
            contentStyle={{ 
              backgroundColor: 'white',
              border: 'none',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
            }}
          />
          <Legend 
            iconType="circle" 
            wrapperStyle={{
              paddingTop: '16px'
            }}
          />
          <Area 
            type="monotone" 
            dataKey="sources" 
            name="Sources Scanned"
            stroke="#6366F1" 
            fillOpacity={1} 
            fill="url(#colorSources)" 
            strokeWidth={2}
          />
          <Area 
            type="monotone" 
            dataKey="verified" 
            name="Sources Verified" 
            stroke="#8B5CF6" 
            fillOpacity={1} 
            fill="url(#colorVerified)" 
            strokeWidth={2}
          />
          <Area 
            type="monotone" 
            dataKey="reports" 
            name="Reports Generated" 
            stroke="#EC4899" 
            fillOpacity={1} 
            fill="url(#colorReports)" 
            strokeWidth={2}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
}
