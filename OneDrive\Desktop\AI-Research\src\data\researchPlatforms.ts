export type ResearchPlatform = {
  id: string;
  name: string;
  description: string;
  url: string;
  searchUrl: string;
  submitUrl?: string;
  apiUrl?: string;
  category: 'academic' | 'preprint' | 'journal' | 'database' | 'repository';
  fields: string[];
  features: {
    search: boolean;
    submit: boolean;
    openAccess: boolean;
    peerReviewed: boolean;
    preprint: boolean;
  };
  logo?: string;
  color: string;
  popularity: number;
  requirements?: string[];
};

export const researchPlatforms: ResearchPlatform[] = [
  {
    id: 'arxiv',
    name: 'arXiv',
    description: 'Open-access repository of electronic preprints and postprints approved for posting after moderation.',
    url: 'https://arxiv.org',
    searchUrl: 'https://arxiv.org/search/?query={query}&searchtype=all',
    submitUrl: 'https://arxiv.org/submit',
    category: 'preprint',
    fields: ['Physics', 'Mathematics', 'Computer Science', 'Quantitative Biology', 'Statistics'],
    features: {
      search: true,
      submit: true,
      openAccess: true,
      peerReviewed: false,
      preprint: true
    },
    color: '#B31B1B',
    popularity: 95,
    requirements: ['Academic affiliation', 'Endorsement for first submission']
  },
  {
    id: 'pubmed',
    name: 'PubMed',
    description: 'Free search engine accessing primarily the MEDLINE database of references and abstracts on life sciences and biomedical topics.',
    url: 'https://pubmed.ncbi.nlm.nih.gov',
    searchUrl: 'https://pubmed.ncbi.nlm.nih.gov/?term={query}',
    category: 'database',
    fields: ['Medicine', 'Life Sciences', 'Biomedical Research', 'Health Sciences'],
    features: {
      search: true,
      submit: false,
      openAccess: true,
      peerReviewed: true,
      preprint: false
    },
    color: '#1F4788',
    popularity: 90
  },
  {
    id: 'ieee',
    name: 'IEEE Xplore',
    description: 'Digital library providing access to the world\'s highest quality technical literature in engineering and technology.',
    url: 'https://ieeexplore.ieee.org',
    searchUrl: 'https://ieeexplore.ieee.org/search/searchresult.jsp?queryText={query}',
    submitUrl: 'https://www.ieee.org/conferences/publishing/index.html',
    category: 'journal',
    fields: ['Engineering', 'Computer Science', 'Electronics', 'Technology'],
    features: {
      search: true,
      submit: true,
      openAccess: false,
      peerReviewed: true,
      preprint: false
    },
    color: '#00629B',
    popularity: 85,
    requirements: ['IEEE membership for submission', 'Peer review process']
  },
  {
    id: 'nature',
    name: 'Nature',
    description: 'International weekly journal publishing the finest peer-reviewed research in all fields of science and technology.',
    url: 'https://www.nature.com',
    searchUrl: 'https://www.nature.com/search?q={query}',
    submitUrl: 'https://www.nature.com/nature/for-authors',
    category: 'journal',
    fields: ['All Sciences', 'Multidisciplinary'],
    features: {
      search: true,
      submit: true,
      openAccess: false,
      peerReviewed: true,
      preprint: false
    },
    color: '#0F7B0F',
    popularity: 95,
    requirements: ['High impact research', 'Rigorous peer review', 'Editorial approval']
  },
  {
    id: 'science',
    name: 'Science',
    description: 'Academic journal published by the American Association for the Advancement of Science.',
    url: 'https://www.science.org',
    searchUrl: 'https://www.science.org/action/doSearch?AllField={query}',
    submitUrl: 'https://www.science.org/content/page/science-information-authors',
    category: 'journal',
    fields: ['All Sciences', 'Multidisciplinary'],
    features: {
      search: true,
      submit: true,
      openAccess: false,
      peerReviewed: true,
      preprint: false
    },
    color: '#D32F2F',
    popularity: 92,
    requirements: ['Breakthrough research', 'Broad scientific interest']
  },
  {
    id: 'plos',
    name: 'PLOS ONE',
    description: 'Peer-reviewed open access scientific journal published by the Public Library of Science.',
    url: 'https://journals.plos.org/plosone',
    searchUrl: 'https://journals.plos.org/plosone/search?q={query}',
    submitUrl: 'https://journals.plos.org/plosone/s/submission-guidelines',
    category: 'journal',
    fields: ['All Sciences', 'Multidisciplinary'],
    features: {
      search: true,
      submit: true,
      openAccess: true,
      peerReviewed: true,
      preprint: false
    },
    color: '#F68B1E',
    popularity: 80,
    requirements: ['Scientific rigor', 'Open access publication fee']
  },
  {
    id: 'biorxiv',
    name: 'bioRxiv',
    description: 'Free online archive and distribution service for unpublished preprints in the life sciences.',
    url: 'https://www.biorxiv.org',
    searchUrl: 'https://www.biorxiv.org/search/{query}',
    submitUrl: 'https://www.biorxiv.org/submit-a-manuscript',
    category: 'preprint',
    fields: ['Biology', 'Life Sciences', 'Medicine'],
    features: {
      search: true,
      submit: true,
      openAccess: true,
      peerReviewed: false,
      preprint: true
    },
    color: '#B8860B',
    popularity: 75
  },
  {
    id: 'researchgate',
    name: 'ResearchGate',
    description: 'Social networking site for scientists and researchers to share papers, ask and answer questions.',
    url: 'https://www.researchgate.net',
    searchUrl: 'https://www.researchgate.net/search?q={query}',
    category: 'repository',
    fields: ['All Sciences', 'Multidisciplinary'],
    features: {
      search: true,
      submit: true,
      openAccess: true,
      peerReviewed: false,
      preprint: true
    },
    color: '#00D0AF',
    popularity: 70
  },
  {
    id: 'springer',
    name: 'Springer',
    description: 'Global publisher dedicated to providing the best possible service to the whole research community.',
    url: 'https://link.springer.com',
    searchUrl: 'https://link.springer.com/search?query={query}',
    submitUrl: 'https://www.springer.com/gp/authors-editors',
    category: 'journal',
    fields: ['All Sciences', 'Technology', 'Medicine'],
    features: {
      search: true,
      submit: true,
      openAccess: false,
      peerReviewed: true,
      preprint: false
    },
    color: '#FFCC02',
    popularity: 82
  },
  {
    id: 'acm',
    name: 'ACM Digital Library',
    description: 'Research, discovery and networking platform containing the full-text collection of all ACM publications.',
    url: 'https://dl.acm.org',
    searchUrl: 'https://dl.acm.org/action/doSearch?AllField={query}',
    submitUrl: 'https://www.acm.org/publications/authors',
    category: 'journal',
    fields: ['Computer Science', 'Information Technology'],
    features: {
      search: true,
      submit: true,
      openAccess: false,
      peerReviewed: true,
      preprint: false
    },
    color: '#0085CA',
    popularity: 78
  },
  {
    id: 'google-scholar',
    name: 'Google Scholar',
    description: 'Web search engine that indexes the full text or metadata of scholarly literature.',
    url: 'https://scholar.google.com',
    searchUrl: 'https://scholar.google.com/scholar?q={query}',
    category: 'database',
    fields: ['All Sciences', 'Multidisciplinary'],
    features: {
      search: true,
      submit: false,
      openAccess: true,
      peerReviewed: true,
      preprint: true
    },
    color: '#4285F4',
    popularity: 98
  },
  {
    id: 'semantic-scholar',
    name: 'Semantic Scholar',
    description: 'AI-powered research tool for scientific literature, using machine learning to help scholars discover relevant research.',
    url: 'https://www.semanticscholar.org',
    searchUrl: 'https://www.semanticscholar.org/search?q={query}',
    apiUrl: 'https://api.semanticscholar.org/graph/v1',
    category: 'database',
    fields: ['All Sciences', 'Computer Science', 'Medicine'],
    features: {
      search: true,
      submit: false,
      openAccess: true,
      peerReviewed: true,
      preprint: true
    },
    color: '#1857B6',
    popularity: 72
  }
];

export const getPlatformsByCategory = (category: ResearchPlatform['category']) => {
  return researchPlatforms.filter(platform => platform.category === category);
};

export const getPlatformsByField = (field: string) => {
  return researchPlatforms.filter(platform => 
    platform.fields.some(f => f.toLowerCase().includes(field.toLowerCase()))
  );
};

export const getTopPlatforms = (limit: number = 5) => {
  return researchPlatforms
    .sort((a, b) => b.popularity - a.popularity)
    .slice(0, limit);
};

export const searchPlatforms = (query: string) => {
  const lowerQuery = query.toLowerCase();
  return researchPlatforms.filter(platform =>
    platform.name.toLowerCase().includes(lowerQuery) ||
    platform.description.toLowerCase().includes(lowerQuery) ||
    platform.fields.some(field => field.toLowerCase().includes(lowerQuery))
  );
};
