import { useState } from 'react';
import { motion } from 'framer-motion';
import { Monitor, Moon, Sun } from 'lucide-react';
import { useDarkMode } from '../hooks/useDarkMode';

export default function ThemeToggle() {
  const { theme, toggleTheme, setTheme } = useDarkMode();
  const [isOpen, setIsOpen] = useState(false);
  
  const handleToggle = () => {
    if (isOpen) {
      setIsOpen(false);
    } else {
      toggleTheme();
    }
  };
  
  const handleSetTheme = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    setIsOpen(false);
  };
  
  return (
    <div className="relative">
      <motion.button
        onClick={handleToggle}
        className="p-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700 transition-all"
        whileTap={{ scale: 0.9 }}
        whileHover={{ backgroundColor: theme === 'dark' ? '#374151' : '#E5E7EB' }}
      >
        {theme === 'dark' ? (
          <Moon size={18} />
        ) : theme === 'light' ? (
          <Sun size={18} />
        ) : (
          <Monitor size={18} />
        )}
      </motion.button>
      
      {isOpen && (
        <motion.div
          initial={{ opacity: 0, y: 10, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: 10, scale: 0.95 }}
          transition={{ duration: 0.1 }}
          className="absolute right-0 mt-2 w-48 rounded-lg shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 z-50"
        >
          <div className="py-1">
            <button
              onClick={() => handleSetTheme('light')}
              className={`flex items-center gap-2 w-full px-4 py-2 text-sm ${
                theme === 'light' 
                  ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20' 
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Sun size={16} />
              Light Mode
            </button>
            <button
              onClick={() => handleSetTheme('dark')}
              className={`flex items-center gap-2 w-full px-4 py-2 text-sm ${
                theme === 'dark' 
                  ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20' 
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Moon size={16} />
              Dark Mode
            </button>
            <button
              onClick={() => handleSetTheme('system')}
              className={`flex items-center gap-2 w-full px-4 py-2 text-sm ${
                theme === 'system' 
                  ? 'text-indigo-600 dark:text-indigo-400 bg-indigo-50 dark:bg-indigo-900/20' 
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
              }`}
            >
              <Monitor size={16} />
              System Preference
            </button>
          </div>
        </motion.div>
      )}
    </div>
  );
}
