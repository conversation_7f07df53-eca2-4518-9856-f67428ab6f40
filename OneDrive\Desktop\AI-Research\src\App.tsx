import { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Plus } from 'lucide-react';
import './index.css';
import DashboardPage from './pages/DashboardPage';
import ReportsPage from './pages/ReportsPage';
import LibraryPage from './pages/LibraryPage';
import BrowsePage from './pages/BrowsePage';
import HistoryPage from './pages/HistoryPage';
import SourcesPage from './pages/SourcesPage';
import TemplatesPage from './pages/TemplatesPage';
import SettingsPage from './pages/SettingsPage';
import { ResearchProvider } from './context/ResearchContext';
import { AnimatePresence } from 'framer-motion';
import Navbar from './components/Navbar';
import Sidebar from './components/Sidebar';
import NewResearchModal from './components/NewResearchModal';
import BackgroundAnimation from './components/BackgroundAnimation';

function AppContent() {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const location = useLocation();

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  // Don't show FAB on settings page
  const showFAB = location.pathname !== '/settings';

  return (
    <div className="flex flex-col h-screen overflow-hidden">
      <BackgroundAnimation />
      <Navbar />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar onNewResearch={handleOpenModal} />
        <AnimatePresence mode="wait">
          <Routes>
            <Route path="/" element={<DashboardPage />} />
            <Route path="/reports" element={<ReportsPage />} />
            <Route path="/library" element={<LibraryPage />} />
            <Route path="/browse" element={<BrowsePage />} />
            <Route path="/history" element={<HistoryPage />} />
            <Route path="/sources" element={<SourcesPage />} />
            <Route path="/templates" element={<TemplatesPage />} />
            <Route path="/settings" element={<SettingsPage />} />
          </Routes>
        </AnimatePresence>
      </div>

      {/* Floating Action Button */}
      {showFAB && (
        <motion.button
          onClick={handleOpenModal}
          className="fixed right-6 bottom-6 p-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow z-50"
          whileHover={{
            scale: 1.05,
            boxShadow: '0 0 20px rgba(99, 102, 241, 0.5)'
          }}
          whileTap={{ scale: 0.95 }}
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 15,
            delay: 0.5
          }}
        >
          <Plus size={24} />
        </motion.button>
      )}

      <NewResearchModal isOpen={isModalOpen} onClose={handleCloseModal} />
    </div>
  );
}

function App() {
  useEffect(() => {
    const link = document.createElement('link');
    link.href = 'https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap';
    link.rel = 'stylesheet';
    document.head.appendChild(link);

    return () => {
      document.head.removeChild(link);
    };
  }, []);

  // Check for dark mode preference
  useEffect(() => {
    const isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches;
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
    }
  }, []);

  return (
    <ResearchProvider>
      <Router>
        <AppContent />
      </Router>
    </ResearchProvider>
  );
}

export default App;
