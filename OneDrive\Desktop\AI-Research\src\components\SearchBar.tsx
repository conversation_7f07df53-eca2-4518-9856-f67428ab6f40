import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BookOpen, ChevronRight, Clock, FileText, Search, X } from 'lucide-react';

export default function SearchBar() {
  const [search, setSearch] = useState('');
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Mock recent searches
  const recentSearches = [
    'Artificial Intelligence Ethics',
    'Quantum Computing Applications',
    'Climate Change Data Analysis'
  ];
  
  // Mock suggested topics
  const suggestedTopics = [
    'Machine Learning',
    'Renewable Energy',
    'Blockchain Technology',
    'Biotechnology Advances'
  ];
  
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setIsFocused(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  return (
    <div className="relative" ref={inputRef}>
      <div className={`flex items-center gap-2 px-3 py-2 bg-gray-100 dark:bg-gray-800 rounded-md w-full md:w-96 transition-all duration-200 ${
        isFocused ? 'ring-2 ring-indigo-300 dark:ring-indigo-600 bg-white dark:bg-gray-900' : ''
      }`}>
        <Search size={18} className={`transition-colors duration-200 ${
          isFocused ? 'text-indigo-500 dark:text-indigo-400' : 'text-gray-500 dark:text-gray-400'
        }`} />
        <input
          type="text"
          placeholder="Search research topics or reports..."
          className="bg-transparent border-none outline-none flex-1 text-sm text-gray-800 dark:text-gray-200"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          onFocus={() => setIsFocused(true)}
        />
        {search && (
          <motion.button
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0 }}
            className="p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700"
            onClick={() => setSearch('')}
          >
            <X size={14} className="text-gray-500 dark:text-gray-400" />
          </motion.button>
        )}
      </div>
      
      <AnimatePresence>
        {isFocused && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute top-full left-0 right-0 mt-2 p-3 bg-white dark:bg-gray-900 shadow-xl rounded-lg border border-gray-200 dark:border-gray-700 z-50"
          >
            {recentSearches.length > 0 && (
              <div className="mb-4">
                <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">Recent Searches</h3>
                <div className="space-y-1">
                  {recentSearches.map((item, index) => (
                    <button
                      key={index}
                      className="flex items-center gap-2 w-full p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 text-left"
                      onClick={() => setSearch(item)}
                    >
                      <Clock size={14} className="text-gray-400 dark:text-gray-500" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{item}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
            
            <div>
              <h3 className="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">Suggested Topics</h3>
              <div className="space-y-1">
                {suggestedTopics.map((topic, index) => (
                  <button
                    key={index}
                    className="flex items-center justify-between w-full p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-800 text-left"
                    onClick={() => setSearch(topic)}
                  >
                    <div className="flex items-center gap-2">
                      <BookOpen size={14} className="text-indigo-500 dark:text-indigo-400" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">{topic}</span>
                    </div>
                    <ChevronRight size={14} className="text-gray-400" />
                  </button>
                ))}
              </div>
            </div>
            
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
              <button className="flex items-center gap-2 w-full p-2 rounded-md bg-indigo-50 dark:bg-indigo-900/20 text-indigo-600 dark:text-indigo-400 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 text-left">
                <FileText size={14} />
                <span className="text-sm font-medium">Advanced Research Options</span>
              </button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
